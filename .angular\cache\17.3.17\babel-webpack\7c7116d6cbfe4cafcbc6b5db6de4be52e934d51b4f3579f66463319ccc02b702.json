{"ast": null, "code": "import { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { mergeMap } from './mergeMap';\nimport { toArray } from './toArray';\nexport function joinAllInternals(joinFn, project) {\n  return pipe(toArray(), mergeMap(sources => joinFn(sources)), project ? mapOneOrManyArgs(project) : identity);\n}\n//# sourceMappingURL=joinAllInternals.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}