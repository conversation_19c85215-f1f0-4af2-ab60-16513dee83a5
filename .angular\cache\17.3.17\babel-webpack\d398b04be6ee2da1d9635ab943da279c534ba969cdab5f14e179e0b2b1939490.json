{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nfunction ProjectsComponent_div_15_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tech_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tech_r4);\n  }\n}\nfunction ProjectsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16);\n    i0.ɵɵelement(2, \"img\", 17);\n    i0.ɵɵelementStart(3, \"div\", 18)(4, \"div\", 19)(5, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_div_15_Template_button_click_5_listener() {\n      const project_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openDemo(project_r2.demoUrl));\n    });\n    i0.ɵɵelement(6, \"i\", 21);\n    i0.ɵɵtext(7, \" Live Demo \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_div_15_Template_button_click_8_listener() {\n      const project_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openGithub(project_r2.githubUrl));\n    });\n    i0.ɵɵelement(9, \"i\", 23);\n    i0.ɵɵtext(10, \" GitHub \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"h3\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 25);\n    i0.ɵɵtemplate(17, ProjectsComponent_div_15_span_17_Template, 2, 1, \"span\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r2 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r5 * 0.2 + \"s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", project_r2.image, i0.ɵɵsanitizeUrl)(\"alt\", project_r2.title);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(project_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r2.longDescription);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", project_r2.technologies);\n  }\n}\nfunction ProjectsComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_button_24_Template_button_click_0_listener() {\n      const category_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectCategory(category_r7.id));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.selectedCategory === category_r7.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r7.name, \" \");\n  }\n}\nfunction ProjectsComponent_div_26_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tech_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tech_r10);\n  }\n}\nfunction ProjectsComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30);\n    i0.ɵɵelement(2, \"img\", 17);\n    i0.ɵɵelementStart(3, \"div\", 31)(4, \"div\", 32)(5, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_div_26_Template_button_click_5_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openDemo(project_r9.demoUrl));\n    });\n    i0.ɵɵelement(6, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_div_26_Template_button_click_7_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openGithub(project_r9.githubUrl));\n    });\n    i0.ɵɵelement(8, \"i\", 23);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"div\", 35)(10, \"h3\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 36);\n    i0.ɵɵtemplate(15, ProjectsComponent_div_26_span_15_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 38)(17, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_div_26_Template_button_click_17_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openDemo(project_r9.demoUrl));\n    });\n    i0.ɵɵtext(18, \" View Project \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const project_r9 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r11 * 0.1 + \"s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", project_r9.image, i0.ɵɵsanitizeUrl)(\"alt\", project_r9.title);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(project_r9.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r9.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", project_r9.technologies);\n  }\n}\nfunction ProjectsComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"No projects found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"No projects match the selected category.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ProjectsComponent = /*#__PURE__*/(() => {\n  class ProjectsComponent {\n    constructor() {\n      this.selectedCategory = 'all';\n      this.categories = [{\n        id: 'all',\n        name: 'All Projects'\n      }, {\n        id: 'web',\n        name: 'Web Apps'\n      }, {\n        id: 'angular',\n        name: 'Angular'\n      }, {\n        id: 'responsive',\n        name: 'Responsive'\n      }];\n      this.projects = [{\n        id: 1,\n        title: 'E-Commerce Dashboard',\n        description: 'Modern admin dashboard for e-commerce management with real-time analytics.',\n        longDescription: 'A comprehensive admin dashboard built with Angular featuring real-time sales analytics, inventory management, and customer insights. Includes responsive design and interactive charts.',\n        image: 'https://via.placeholder.com/400x250/667eea/ffffff?text=E-Commerce+Dashboard',\n        technologies: ['Angular', 'TypeScript', 'SCSS', 'Chart.js'],\n        demoUrl: '#',\n        githubUrl: '#',\n        featured: true,\n        category: 'angular'\n      }, {\n        id: 2,\n        title: 'Task Management App',\n        description: 'Collaborative task management application with drag-and-drop functionality.',\n        longDescription: 'A Kanban-style task management application that allows teams to organize projects, assign tasks, and track progress with an intuitive drag-and-drop interface.',\n        image: 'https://via.placeholder.com/400x250/764ba2/ffffff?text=Task+Manager',\n        technologies: ['Angular', 'RxJS', 'Angular CDK', 'SCSS'],\n        demoUrl: '#',\n        githubUrl: '#',\n        featured: true,\n        category: 'web'\n      }, {\n        id: 3,\n        title: 'Weather App',\n        description: 'Beautiful weather application with location-based forecasts and animations.',\n        longDescription: 'A responsive weather application that provides current weather conditions and 7-day forecasts with smooth animations and beautiful UI design.',\n        image: 'https://via.placeholder.com/400x250/4ecdc4/ffffff?text=Weather+App',\n        technologies: ['JavaScript', 'CSS3', 'Weather API', 'HTML5'],\n        demoUrl: '#',\n        githubUrl: '#',\n        featured: false,\n        category: 'web'\n      }, {\n        id: 4,\n        title: 'Portfolio Website',\n        description: 'Personal portfolio website showcasing projects and skills.',\n        longDescription: 'This very portfolio website built with Angular, featuring responsive design, smooth animations, and modern UI components.',\n        image: 'https://via.placeholder.com/400x250/667eea/ffffff?text=Portfolio+Site',\n        technologies: ['Angular', 'TypeScript', 'SCSS', 'Responsive Design'],\n        demoUrl: '#',\n        githubUrl: '#',\n        featured: true,\n        category: 'responsive'\n      }, {\n        id: 5,\n        title: 'Restaurant Landing Page',\n        description: 'Elegant landing page for a restaurant with online reservation system.',\n        longDescription: 'A beautiful, responsive landing page for a restaurant featuring menu showcase, online reservations, and contact information with smooth scrolling effects.',\n        image: 'https://via.placeholder.com/400x250/ff6b6b/ffffff?text=Restaurant+Site',\n        technologies: ['HTML5', 'CSS3', 'JavaScript', 'Responsive Design'],\n        demoUrl: '#',\n        githubUrl: '#',\n        featured: false,\n        category: 'responsive'\n      }, {\n        id: 6,\n        title: 'Blog Platform',\n        description: 'Modern blog platform with content management and user authentication.',\n        longDescription: 'A full-featured blog platform with user authentication, content management, commenting system, and responsive design.',\n        image: 'https://via.placeholder.com/400x250/45b7d1/ffffff?text=Blog+Platform',\n        technologies: ['Angular', 'TypeScript', 'SCSS', 'Firebase'],\n        demoUrl: '#',\n        githubUrl: '#',\n        featured: false,\n        category: 'angular'\n      }];\n    }\n    get filteredProjects() {\n      if (this.selectedCategory === 'all') {\n        return this.projects;\n      }\n      return this.projects.filter(project => project.category === this.selectedCategory);\n    }\n    get featuredProjects() {\n      return this.projects.filter(project => project.featured);\n    }\n    selectCategory(categoryId) {\n      this.selectedCategory = categoryId;\n    }\n    openDemo(url) {\n      if (url !== '#') {\n        window.open(url, '_blank');\n      }\n    }\n    openGithub(url) {\n      if (url !== '#') {\n        window.open(url, '_blank');\n      }\n    }\n    static {\n      this.ɵfac = function ProjectsComponent_Factory(t) {\n        return new (t || ProjectsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ProjectsComponent,\n        selectors: [[\"app-projects\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 37,\n        vars: 4,\n        consts: [[1, \"projects-hero\", \"section\"], [1, \"container\"], [1, \"section-title\"], [1, \"featured-projects\", \"section\"], [1, \"featured-grid\"], [\"class\", \"featured-project fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"all-projects\", \"section\"], [1, \"category-filter\"], [\"class\", \"filter-btn\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"projects-grid\"], [\"class\", \"project-card fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"cta-section\", \"section\"], [1, \"cta-content\"], [\"routerLink\", \"/contact\", 1, \"btn\", \"btn-primary\"], [1, \"featured-project\", \"fade-in-up\"], [1, \"project-image\"], [3, \"src\", \"alt\"], [1, \"project-overlay\"], [1, \"project-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"btn\", \"btn-outline\", 3, \"click\"], [1, \"fab\", \"fa-github\"], [1, \"project-content\"], [1, \"project-tech\"], [\"class\", \"tech-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"tech-tag\"], [1, \"filter-btn\", 3, \"click\"], [1, \"project-card\", \"fade-in-up\"], [1, \"card-image\"], [1, \"card-overlay\"], [1, \"card-actions\"], [\"title\", \"Live Demo\", 1, \"action-btn\", 3, \"click\"], [\"title\", \"GitHub\", 1, \"action-btn\", 3, \"click\"], [1, \"card-content\"], [1, \"card-tech\"], [\"class\", \"tech-badge\", 4, \"ngFor\", \"ngForOf\"], [1, \"card-footer\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"tech-badge\"], [1, \"empty-state\"], [1, \"fas\", \"fa-folder-open\"]],\n        template: function ProjectsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n            i0.ɵɵtext(4, \"My Projects\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"A showcase of my work and the technologies I've mastered\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(7, \"section\", 3)(8, \"div\", 1)(9, \"div\", 2)(10, \"h2\");\n            i0.ɵɵtext(11, \"Featured Projects\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"p\");\n            i0.ɵɵtext(13, \"Highlighting my best work and most challenging projects\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 4);\n            i0.ɵɵtemplate(15, ProjectsComponent_div_15_Template, 18, 7, \"div\", 5);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"section\", 6)(17, \"div\", 1)(18, \"div\", 2)(19, \"h2\");\n            i0.ɵɵtext(20, \"All Projects\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"p\");\n            i0.ɵɵtext(22, \"Browse through all my projects by category\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(23, \"div\", 7);\n            i0.ɵɵtemplate(24, ProjectsComponent_button_24_Template, 2, 3, \"button\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"div\", 9);\n            i0.ɵɵtemplate(26, ProjectsComponent_div_26_Template, 19, 7, \"div\", 10);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(27, ProjectsComponent_div_27_Template, 6, 0, \"div\", 11);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"section\", 12)(29, \"div\", 1)(30, \"div\", 13)(31, \"h2\");\n            i0.ɵɵtext(32, \"Interested in Working Together?\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"p\");\n            i0.ɵɵtext(34, \"I'm always open to discussing new opportunities and exciting projects.\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"a\", 14);\n            i0.ɵɵtext(36, \"Get In Touch\");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngForOf\", ctx.featuredProjects);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngForOf\", ctx.filteredProjects);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.filteredProjects.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, i2.RouterLink],\n        styles: [\".projects-hero[_ngcontent-%COMP%]{padding-top:120px;background:linear-gradient(135deg,#f8f9fa,#e9ecef)}.featured-projects[_ngcontent-%COMP%]{background:#fff}.featured-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(500px,1fr));gap:40px}.featured-project[_ngcontent-%COMP%]{background:#fff;border-radius:15px;overflow:hidden;box-shadow:0 10px 30px #0000001a;transition:transform .3s ease}.featured-project[_ngcontent-%COMP%]:hover{transform:translateY(-5px)}.project-image[_ngcontent-%COMP%]{position:relative;height:250px;overflow:hidden}.project-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.project-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%]{transform:scale(1.05)}.project-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:#000c;display:flex;align-items:center;justify-content:center;opacity:0;transition:opacity .3s ease}.project-image[_ngcontent-%COMP%]:hover   .project-overlay[_ngcontent-%COMP%]{opacity:1}.project-actions[_ngcontent-%COMP%]{display:flex;gap:15px}.project-content[_ngcontent-%COMP%]{padding:30px}.project-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.5rem;color:#2c3e50;margin-bottom:15px}.project-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;line-height:1.6;margin-bottom:20px}.project-tech[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:10px}.tech-tag[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:5px 12px;border-radius:20px;font-size:.85rem;font-weight:500}.all-projects[_ngcontent-%COMP%]{background:#f8f9fa}.category-filter[_ngcontent-%COMP%]{display:flex;justify-content:center;flex-wrap:wrap;gap:15px;margin-bottom:50px}.filter-btn[_ngcontent-%COMP%]{padding:10px 25px;border:2px solid #667eea;background:transparent;color:#667eea;border-radius:25px;font-weight:500;cursor:pointer;transition:all .3s ease}.filter-btn[_ngcontent-%COMP%]:hover, .filter-btn.active[_ngcontent-%COMP%]{background:#667eea;color:#fff}.projects-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:30px}.project-card[_ngcontent-%COMP%]{background:#fff;border-radius:15px;overflow:hidden;box-shadow:0 5px 20px #00000014;transition:all .3s ease}.project-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 15px 30px #00000026}.card-image[_ngcontent-%COMP%]{position:relative;height:200px;overflow:hidden}.card-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.card-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%]{transform:scale(1.05)}.card-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:#000000b3;display:flex;align-items:center;justify-content:center;opacity:0;transition:opacity .3s ease}.card-image[_ngcontent-%COMP%]:hover   .card-overlay[_ngcontent-%COMP%]{opacity:1}.card-actions[_ngcontent-%COMP%]{display:flex;gap:15px}.action-btn[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:50%;border:2px solid white;background:transparent;color:#fff;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.action-btn[_ngcontent-%COMP%]:hover{background:#fff;color:#333}.card-content[_ngcontent-%COMP%]{padding:25px}.card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.3rem;color:#2c3e50;margin-bottom:10px}.card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;line-height:1.6;margin-bottom:20px;font-size:.95rem}.card-tech[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:20px}.tech-badge[_ngcontent-%COMP%]{background:#f8f9fa;color:#667eea;padding:4px 10px;border-radius:15px;font-size:.8rem;font-weight:500;border:1px solid #e9ecef}.card-footer[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%]{padding:8px 20px;font-size:.9rem}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:60px 20px;color:#666}.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;margin-bottom:20px;opacity:.5}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:10px;color:#2c3e50}.cta-section[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;text-align:center}.cta-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:2.5rem;margin-bottom:20px}.cta-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.2rem;margin-bottom:30px;opacity:.9}.cta-content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{background:#fff;color:#667eea}.cta-content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{background:#f8f9fa;transform:translateY(-2px)}@media (max-width: 768px){.featured-grid[_ngcontent-%COMP%], .projects-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.category-filter[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.project-actions[_ngcontent-%COMP%], .card-actions[_ngcontent-%COMP%]{flex-direction:column;gap:10px}.cta-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:2rem}}@media (max-width: 480px){.project-content[_ngcontent-%COMP%], .card-content[_ngcontent-%COMP%]{padding:20px}.featured-project[_ngcontent-%COMP%], .project-card[_ngcontent-%COMP%]{margin:0 10px}}\"]\n      });\n    }\n  }\n  return ProjectsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}