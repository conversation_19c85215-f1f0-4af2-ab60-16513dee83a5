{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = () => ({\n  exact: true\n});\nexport class NavbarComponent {\n  constructor() {\n    this.isMenuOpen = false;\n    this.isScrolled = false;\n  }\n  ngOnInit() {\n    window.addEventListener('scroll', () => {\n      this.isScrolled = window.scrollY > 50;\n    });\n  }\n  toggleMenu() {\n    this.isMenuOpen = !this.isMenuOpen;\n  }\n  closeMenu() {\n    this.isMenuOpen = false;\n  }\n  static {\n    this.ɵfac = function NavbarComponent_Factory(t) {\n      return new (t || NavbarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavbarComponent,\n      selectors: [[\"app-navbar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 44,\n      vars: 14,\n      consts: [[1, \"navbar\"], [1, \"container\"], [1, \"nav-content\"], [1, \"logo\"], [\"routerLink\", \"/\", 3, \"click\"], [1, \"logo-text\"], [1, \"nav-links\", \"desktop-nav\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 3, \"routerLinkActiveOptions\"], [\"routerLink\", \"/about\", \"routerLinkActive\", \"active\"], [\"routerLink\", \"/skills\", \"routerLinkActive\", \"active\"], [\"routerLink\", \"/projects\", \"routerLinkActive\", \"active\"], [\"routerLink\", \"/contact\", \"routerLinkActive\", \"active\"], [1, \"mobile-menu-btn\", 3, \"click\"], [1, \"mobile-nav\"], [1, \"nav-links\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 3, \"click\", \"routerLinkActiveOptions\"], [\"routerLink\", \"/about\", \"routerLinkActive\", \"active\", 3, \"click\"], [\"routerLink\", \"/skills\", \"routerLinkActive\", \"active\", 3, \"click\"], [\"routerLink\", \"/projects\", \"routerLinkActive\", \"active\", 3, \"click\"], [\"routerLink\", \"/contact\", \"routerLinkActive\", \"active\", 3, \"click\"]],\n      template: function NavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_4_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6, \"Abdullah G\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"ul\", 6)(8, \"li\")(9, \"a\", 7);\n          i0.ɵɵtext(10, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"li\")(12, \"a\", 8);\n          i0.ɵɵtext(13, \"About\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"li\")(15, \"a\", 9);\n          i0.ɵɵtext(16, \"Skills\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"li\")(18, \"a\", 10);\n          i0.ɵɵtext(19, \"Projects\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"li\")(21, \"a\", 11);\n          i0.ɵɵtext(22, \"Contact\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 12);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_div_click_23_listener() {\n            return ctx.toggleMenu();\n          });\n          i0.ɵɵelement(24, \"span\")(25, \"span\")(26, \"span\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 13)(28, \"ul\", 14)(29, \"li\")(30, \"a\", 15);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_30_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵtext(31, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"li\")(33, \"a\", 16);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_33_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵtext(34, \"About\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"li\")(36, \"a\", 17);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_36_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵtext(37, \"Skills\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"li\")(39, \"a\", 18);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_39_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵtext(40, \"Projects\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\")(42, \"a\", 19);\n          i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_42_listener() {\n            return ctx.closeMenu();\n          });\n          i0.ɵɵtext(43, \"Contact\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"scrolled\", ctx.isScrolled);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(12, _c0));\n          i0.ɵɵadvance(15);\n          i0.ɵɵclassProp(\"active\", ctx.isMenuOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isMenuOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isMenuOpen);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isMenuOpen);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(13, _c0));\n        }\n      },\n      dependencies: [CommonModule, RouterModule, i1.RouterLink, i1.RouterLinkActive],\n      styles: [\".navbar[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  background: rgba(255, 255, 255, 0.95);\\n  backdrop-filter: blur(10px);\\n  z-index: 1000;\\n  transition: all 0.3s ease;\\n  padding: 15px 0;\\n}\\n.navbar.scrolled[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.98);\\n  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);\\n  padding: 10px 0;\\n}\\n\\n.nav-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  text-decoration: none;\\n}\\n\\n.desktop-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  gap: 30px;\\n}\\n.desktop-nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: #333;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.desktop-nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .desktop-nav[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n.desktop-nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  transition: width 0.3s ease;\\n}\\n.desktop-nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover::after, .desktop-nav[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%]::after {\\n  width: 100%;\\n}\\n\\n.mobile-menu-btn[_ngcontent-%COMP%] {\\n  display: none;\\n  flex-direction: column;\\n  cursor: pointer;\\n  padding: 5px;\\n}\\n.mobile-menu-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: 25px;\\n  height: 3px;\\n  background: #333;\\n  margin: 3px 0;\\n  transition: all 0.3s ease;\\n  border-radius: 2px;\\n}\\n.mobile-menu-btn[_ngcontent-%COMP%]   span.active[_ngcontent-%COMP%]:nth-child(1) {\\n  transform: rotate(45deg) translate(6px, 6px);\\n}\\n.mobile-menu-btn[_ngcontent-%COMP%]   span.active[_ngcontent-%COMP%]:nth-child(2) {\\n  opacity: 0;\\n}\\n.mobile-menu-btn[_ngcontent-%COMP%]   span.active[_ngcontent-%COMP%]:nth-child(3) {\\n  transform: rotate(-45deg) translate(6px, -6px);\\n}\\n\\n.mobile-nav[_ngcontent-%COMP%] {\\n  display: none;\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);\\n  transform: translateY(-20px);\\n  opacity: 0;\\n  visibility: hidden;\\n  transition: all 0.3s ease;\\n}\\n.mobile-nav.active[_ngcontent-%COMP%] {\\n  display: block;\\n  transform: translateY(0);\\n  opacity: 1;\\n  visibility: visible;\\n}\\n.mobile-nav[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 20px 0;\\n}\\n.mobile-nav[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n.mobile-nav[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: block;\\n  padding: 15px 20px;\\n  text-decoration: none;\\n  color: #333;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.mobile-nav[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .mobile-nav[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #667eea;\\n}\\n\\n@media (max-width: 768px) {\\n  .desktop-nav[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .mobile-menu-btn[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n  .mobile-nav[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "NavbarComponent", "constructor", "isMenuOpen", "isScrolled", "ngOnInit", "window", "addEventListener", "scrollY", "toggleMenu", "closeMenu", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NavbarComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "NavbarComponent_Template_a_click_4_listener", "ɵɵtext", "ɵɵelementEnd", "NavbarComponent_Template_div_click_23_listener", "ɵɵelement", "NavbarComponent_Template_a_click_30_listener", "NavbarComponent_Template_a_click_33_listener", "NavbarComponent_Template_a_click_36_listener", "NavbarComponent_Template_a_click_39_listener", "NavbarComponent_Template_a_click_42_listener", "ɵɵclassProp", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "i1", "RouterLink", "RouterLinkActive", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\components\\navbar\\navbar.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\components\\navbar\\navbar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n@Component({\n  selector: 'app-navbar',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  templateUrl: './navbar.component.html',\n  styleUrls: ['./navbar.component.scss']\n})\nexport class NavbarComponent implements OnInit {\n  isMenuOpen = false;\n  isScrolled = false;\n\n  ngOnInit() {\n    window.addEventListener('scroll', () => {\n      this.isScrolled = window.scrollY > 50;\n    });\n  }\n\n  toggleMenu() {\n    this.isMenuOpen = !this.isMenuOpen;\n  }\n\n  closeMenu() {\n    this.isMenuOpen = false;\n  }\n}\n", "<nav class=\"navbar\" [class.scrolled]=\"isScrolled\">\n  <div class=\"container\">\n    <div class=\"nav-content\">\n      <!-- Logo -->\n      <div class=\"logo\">\n        <a routerLink=\"/\" (click)=\"closeMenu()\">\n          <span class=\"logo-text\"><PERSON> G</span>\n        </a>\n      </div>\n\n      <!-- Desktop Navigation -->\n      <ul class=\"nav-links desktop-nav\">\n        <li><a routerLink=\"/\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{exact: true}\">Home</a></li>\n        <li><a routerLink=\"/about\" routerLinkActive=\"active\">About</a></li>\n        <li><a routerLink=\"/skills\" routerLinkActive=\"active\">Skills</a></li>\n        <li><a routerLink=\"/projects\" routerLinkActive=\"active\">Projects</a></li>\n        <li><a routerLink=\"/contact\" routerLinkActive=\"active\">Contact</a></li>\n      </ul>\n\n      <!-- Mobile Menu Button -->\n      <div class=\"mobile-menu-btn\" (click)=\"toggleMenu()\">\n        <span [class.active]=\"isMenuOpen\"></span>\n        <span [class.active]=\"isMenuOpen\"></span>\n        <span [class.active]=\"isMenuOpen\"></span>\n      </div>\n    </div>\n\n    <!-- Mobile Navigation -->\n    <div class=\"mobile-nav\" [class.active]=\"isMenuOpen\">\n      <ul class=\"nav-links\">\n        <li><a routerLink=\"/\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{exact: true}\" (click)=\"closeMenu()\">Home</a></li>\n        <li><a routerLink=\"/about\" routerLinkActive=\"active\" (click)=\"closeMenu()\">About</a></li>\n        <li><a routerLink=\"/skills\" routerLinkActive=\"active\" (click)=\"closeMenu()\">Skills</a></li>\n        <li><a routerLink=\"/projects\" routerLinkActive=\"active\" (click)=\"closeMenu()\">Projects</a></li>\n        <li><a routerLink=\"/contact\" routerLinkActive=\"active\" (click)=\"closeMenu()\">Contact</a></li>\n      </ul>\n    </div>\n  </div>\n</nav>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;;;AAS9C,OAAM,MAAOC,eAAe;EAP5BC,YAAA;IAQE,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;;EAElBC,QAAQA,CAAA;IACNC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAACH,UAAU,GAAGE,MAAM,CAACE,OAAO,GAAG,EAAE;IACvC,CAAC,CAAC;EACJ;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACN,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEAO,SAASA,CAAA;IACP,IAAI,CAACP,UAAU,GAAG,KAAK;EACzB;;;uBAhBWF,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAU,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNpBP,EALR,CAAAS,cAAA,aAAkD,aACzB,aACI,aAEL,WACwB;UAAtBT,EAAA,CAAAU,UAAA,mBAAAC,4CAAA;YAAA,OAASH,GAAA,CAAAZ,SAAA,EAAW;UAAA,EAAC;UACrCI,EAAA,CAAAS,cAAA,cAAwB;UAAAT,EAAA,CAAAY,MAAA,iBAAU;UAEtCZ,EAFsC,CAAAa,YAAA,EAAO,EACvC,EACA;UAIAb,EADN,CAAAS,cAAA,YAAkC,SAC5B,WAAsF;UAAAT,EAAA,CAAAY,MAAA,YAAI;UAAIZ,EAAJ,CAAAa,YAAA,EAAI,EAAK;UACnGb,EAAJ,CAAAS,cAAA,UAAI,YAAiD;UAAAT,EAAA,CAAAY,MAAA,aAAK;UAAIZ,EAAJ,CAAAa,YAAA,EAAI,EAAK;UAC/Db,EAAJ,CAAAS,cAAA,UAAI,YAAkD;UAAAT,EAAA,CAAAY,MAAA,cAAM;UAAIZ,EAAJ,CAAAa,YAAA,EAAI,EAAK;UACjEb,EAAJ,CAAAS,cAAA,UAAI,aAAoD;UAAAT,EAAA,CAAAY,MAAA,gBAAQ;UAAIZ,EAAJ,CAAAa,YAAA,EAAI,EAAK;UACrEb,EAAJ,CAAAS,cAAA,UAAI,aAAmD;UAAAT,EAAA,CAAAY,MAAA,eAAO;UAChEZ,EADgE,CAAAa,YAAA,EAAI,EAAK,EACpE;UAGLb,EAAA,CAAAS,cAAA,eAAoD;UAAvBT,EAAA,CAAAU,UAAA,mBAAAI,+CAAA;YAAA,OAASN,GAAA,CAAAb,UAAA,EAAY;UAAA,EAAC;UAGjDK,EAFA,CAAAe,SAAA,YAAyC,YACA,YACA;UAE7Cf,EADE,CAAAa,YAAA,EAAM,EACF;UAKEb,EAFR,CAAAS,cAAA,eAAoD,cAC5B,UAChB,aAA4G;UAAtBT,EAAA,CAAAU,UAAA,mBAAAM,6CAAA;YAAA,OAASR,GAAA,CAAAZ,SAAA,EAAW;UAAA,EAAC;UAACI,EAAA,CAAAY,MAAA,YAAI;UAAIZ,EAAJ,CAAAa,YAAA,EAAI,EAAK;UACzHb,EAAJ,CAAAS,cAAA,UAAI,aAAuE;UAAtBT,EAAA,CAAAU,UAAA,mBAAAO,6CAAA;YAAA,OAAST,GAAA,CAAAZ,SAAA,EAAW;UAAA,EAAC;UAACI,EAAA,CAAAY,MAAA,aAAK;UAAIZ,EAAJ,CAAAa,YAAA,EAAI,EAAK;UACrFb,EAAJ,CAAAS,cAAA,UAAI,aAAwE;UAAtBT,EAAA,CAAAU,UAAA,mBAAAQ,6CAAA;YAAA,OAASV,GAAA,CAAAZ,SAAA,EAAW;UAAA,EAAC;UAACI,EAAA,CAAAY,MAAA,cAAM;UAAIZ,EAAJ,CAAAa,YAAA,EAAI,EAAK;UACvFb,EAAJ,CAAAS,cAAA,UAAI,aAA0E;UAAtBT,EAAA,CAAAU,UAAA,mBAAAS,6CAAA;YAAA,OAASX,GAAA,CAAAZ,SAAA,EAAW;UAAA,EAAC;UAACI,EAAA,CAAAY,MAAA,gBAAQ;UAAIZ,EAAJ,CAAAa,YAAA,EAAI,EAAK;UAC3Fb,EAAJ,CAAAS,cAAA,UAAI,aAAyE;UAAtBT,EAAA,CAAAU,UAAA,mBAAAU,6CAAA;YAAA,OAASZ,GAAA,CAAAZ,SAAA,EAAW;UAAA,EAAC;UAACI,EAAA,CAAAY,MAAA,eAAO;UAI5FZ,EAJ4F,CAAAa,YAAA,EAAI,EAAK,EAC1F,EACD,EACF,EACF;;;UAtCcb,EAAA,CAAAqB,WAAA,aAAAb,GAAA,CAAAlB,UAAA,CAA6B;UAYOU,EAAA,CAAAsB,SAAA,GAAyC;UAAzCtB,EAAA,CAAAuB,UAAA,4BAAAvB,EAAA,CAAAwB,eAAA,KAAAC,GAAA,EAAyC;UASnFzB,EAAA,CAAAsB,SAAA,IAA2B;UAA3BtB,EAAA,CAAAqB,WAAA,WAAAb,GAAA,CAAAnB,UAAA,CAA2B;UAC3BW,EAAA,CAAAsB,SAAA,EAA2B;UAA3BtB,EAAA,CAAAqB,WAAA,WAAAb,GAAA,CAAAnB,UAAA,CAA2B;UAC3BW,EAAA,CAAAsB,SAAA,EAA2B;UAA3BtB,EAAA,CAAAqB,WAAA,WAAAb,GAAA,CAAAnB,UAAA,CAA2B;UAKbW,EAAA,CAAAsB,SAAA,EAA2B;UAA3BtB,EAAA,CAAAqB,WAAA,WAAAb,GAAA,CAAAnB,UAAA,CAA2B;UAECW,EAAA,CAAAsB,SAAA,GAAyC;UAAzCtB,EAAA,CAAAuB,UAAA,4BAAAvB,EAAA,CAAAwB,eAAA,KAAAC,GAAA,EAAyC;;;qBDvBrFxC,YAAY,EAAEC,YAAY,EAAAwC,EAAA,CAAAC,UAAA,EAAAD,EAAA,CAAAE,gBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}