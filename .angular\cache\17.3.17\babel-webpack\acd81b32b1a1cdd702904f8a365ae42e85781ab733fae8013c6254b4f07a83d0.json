{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport class FooterComponent {\n  constructor() {\n    this.currentYear = new Date().getFullYear();\n  }\n  static {\n    this.ɵfac = function FooterComponent_Factory(t) {\n      return new (t || FooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FooterComponent,\n      selectors: [[\"app-footer\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 1,\n      consts: [[1, \"footer\"], [1, \"container\"], [1, \"footer-content\"], [1, \"footer-text\"], [1, \"social-links\"], [\"href\", \"https://github.com\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\"], [1, \"fab\", \"fa-github\"], [\"href\", \"https://linkedin.com\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\"], [1, \"fab\", \"fa-linkedin\"], [\"href\", \"mailto:<EMAIL>\"], [1, \"fas\", \"fa-envelope\"]],\n      template: function FooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"footer\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"p\");\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 4)(7, \"a\", 5);\n          i0.ɵɵelement(8, \"i\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"a\", 7);\n          i0.ɵɵelement(10, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"a\", 9);\n          i0.ɵɵelement(12, \"i\", 10);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\\u00A9 \", ctx.currentYear, \" Abdullah G | All Rights Reserved\");\n        }\n      },\n      dependencies: [CommonModule],\n      styles: [\".footer[_ngcontent-%COMP%] {\\n  background: #2c3e50;\\n  color: white;\\n  padding: 30px 0;\\n  margin-top: auto;\\n}\\n\\n.footer-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n}\\n\\n.footer-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  color: #bdc3c7;\\n}\\n\\n.social-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  background: rgba(255, 255, 255, 0.1);\\n  color: white;\\n  border-radius: 50%;\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n}\\n.social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  background: #667eea;\\n  transform: translateY(-2px);\\n}\\n.social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .footer-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FooterComponent", "constructor", "currentYear", "Date", "getFullYear", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FooterComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\components\\footer\\footer.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\components\\footer\\footer.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-footer',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './footer.component.html',\n  styleUrls: ['./footer.component.scss']\n})\nexport class FooterComponent {\n  currentYear = new Date().getFullYear();\n}\n", "<footer class=\"footer\">\n  <div class=\"container\">\n    <div class=\"footer-content\">\n      <div class=\"footer-text\">\n        <p>&copy; {{ currentYear }} <PERSON> | All Rights Reserved</p>\n      </div>\n      <div class=\"social-links\">\n        <a href=\"https://github.com\" target=\"_blank\" rel=\"noopener noreferrer\">\n          <i class=\"fab fa-github\"></i>\n        </a>\n        <a href=\"https://linkedin.com\" target=\"_blank\" rel=\"noopener noreferrer\">\n          <i class=\"fab fa-linkedin\"></i>\n        </a>\n        <a href=\"mailto:<EMAIL>\">\n          <i class=\"fas fa-envelope\"></i>\n        </a>\n      </div>\n    </div>\n  </div>\n</footer>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;AAS9C,OAAM,MAAOC,eAAe;EAP5BC,YAAA;IAQE,KAAAC,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;;;;uBAD3BJ,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAK,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNpBP,EAJR,CAAAS,cAAA,gBAAuB,aACE,aACO,aACD,QACpB;UAAAT,EAAA,CAAAU,MAAA,GAAyD;UAC9DV,EAD8D,CAAAW,YAAA,EAAI,EAC5D;UAEJX,EADF,CAAAS,cAAA,aAA0B,WAC+C;UACrET,EAAA,CAAAY,SAAA,WAA6B;UAC/BZ,EAAA,CAAAW,YAAA,EAAI;UACJX,EAAA,CAAAS,cAAA,WAAyE;UACvET,EAAA,CAAAY,SAAA,YAA+B;UACjCZ,EAAA,CAAAW,YAAA,EAAI;UACJX,EAAA,CAAAS,cAAA,YAAsC;UACpCT,EAAA,CAAAY,SAAA,aAA+B;UAKzCZ,EAJQ,CAAAW,YAAA,EAAI,EACA,EACF,EACF,EACC;;;UAfEX,EAAA,CAAAa,SAAA,GAAyD;UAAzDb,EAAA,CAAAc,kBAAA,YAAAN,GAAA,CAAAd,WAAA,sCAAyD;;;qBDExDH,YAAY;MAAAwB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}