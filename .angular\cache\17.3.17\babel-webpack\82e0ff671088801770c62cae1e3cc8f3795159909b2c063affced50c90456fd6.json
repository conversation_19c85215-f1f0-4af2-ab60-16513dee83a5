{"ast": null, "code": "export const routes = [{\n  path: '',\n  loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent)\n}, {\n  path: 'about',\n  loadComponent: () => import('./pages/about/about.component').then(m => m.AboutComponent)\n}, {\n  path: 'skills',\n  loadComponent: () => import('./pages/skills/skills.component').then(m => m.SkillsComponent)\n}, {\n  path: 'projects',\n  loadComponent: () => import('./pages/projects/projects.component').then(m => m.ProjectsComponent)\n}, {\n  path: 'contact',\n  loadComponent: () => import('./pages/contact/contact.component').then(m => m.ContactComponent)\n}, {\n  path: '**',\n  redirectTo: ''\n}];", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}