{"ast": null, "code": "import { Observable } from '../Observable';\nexport const EMPTY = new Observable(subscriber => subscriber.complete());\nexport function empty(scheduler) {\n  return scheduler ? emptyScheduled(scheduler) : EMPTY;\n}\nfunction emptyScheduled(scheduler) {\n  return new Observable(subscriber => scheduler.schedule(() => subscriber.complete()));\n}", "map": {"version": 3, "names": ["Observable", "EMPTY", "subscriber", "complete", "empty", "scheduler", "emptyScheduled", "schedule"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/my folio/node_modules/rxjs/dist/esm/internal/observable/empty.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport const EMPTY = new Observable((subscriber) => subscriber.complete());\nexport function empty(scheduler) {\n    return scheduler ? emptyScheduled(scheduler) : EMPTY;\n}\nfunction emptyScheduled(scheduler) {\n    return new Observable((subscriber) => scheduler.schedule(() => subscriber.complete()));\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,OAAO,MAAMC,KAAK,GAAG,IAAID,UAAU,CAAEE,UAAU,IAAKA,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC;AAC1E,OAAO,SAASC,KAAKA,CAACC,SAAS,EAAE;EAC7B,OAAOA,SAAS,GAAGC,cAAc,CAACD,SAAS,CAAC,GAAGJ,KAAK;AACxD;AACA,SAASK,cAAcA,CAACD,SAAS,EAAE;EAC/B,OAAO,IAAIL,UAAU,CAAEE,UAAU,IAAKG,SAAS,CAACE,QAAQ,CAAC,MAAML,UAAU,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC1F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}