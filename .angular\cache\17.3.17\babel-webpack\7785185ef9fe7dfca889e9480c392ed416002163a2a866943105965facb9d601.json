{"ast": null, "code": "import { identity } from './identity';\nexport function pipe(...fns) {\n  return pipeFromArray(fns);\n}\nexport function pipeFromArray(fns) {\n  if (fns.length === 0) {\n    return identity;\n  }\n  if (fns.length === 1) {\n    return fns[0];\n  }\n  return function piped(input) {\n    return fns.reduce((prev, fn) => fn(prev), input);\n  };\n}\n//# sourceMappingURL=pipe.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}