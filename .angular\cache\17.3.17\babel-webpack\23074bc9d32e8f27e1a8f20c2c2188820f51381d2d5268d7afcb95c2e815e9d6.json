{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction AboutComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r2 * 0.1 + \"s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.number);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.label);\n  }\n}\nfunction AboutComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"div\", 27);\n    i0.ɵɵelementStart(2, \"div\", 28)(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r4 * 0.2 + \"s\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r3.year);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.description);\n  }\n}\nexport let AboutComponent = /*#__PURE__*/(() => {\n  class AboutComponent {\n    constructor() {\n      this.stats = [{\n        number: '2024',\n        label: 'Started Journey'\n      }, {\n        number: '10+',\n        label: 'Projects Completed'\n      }, {\n        number: '4+',\n        label: 'Technologies Mastered'\n      }, {\n        number: '100%',\n        label: 'Passion for Code'\n      }];\n      this.timeline = [{\n        year: '2024',\n        title: 'Started My Development Journey',\n        description: 'Began learning web development with a focus on modern frontend technologies and best practices.'\n      }, {\n        year: '2024',\n        title: 'Mastered Angular Framework',\n        description: 'Specialized in Angular development, creating responsive and dynamic web applications.'\n      }, {\n        year: '2024',\n        title: 'Launched Portfolio',\n        description: 'Created this portfolio to showcase my skills and projects to potential clients and employers.'\n      }];\n    }\n    static {\n      this.ɵfac = function AboutComponent_Factory(t) {\n        return new (t || AboutComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AboutComponent,\n        selectors: [[\"app-about\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 59,\n        vars: 2,\n        consts: [[1, \"about-hero\", \"section\"], [1, \"container\"], [1, \"about-content\"], [1, \"about-image\", \"fade-in\"], [1, \"image-placeholder\"], [1, \"fas\", \"fa-user\"], [1, \"about-text\", \"fade-in-up\"], [1, \"stats-section\"], [1, \"stats-grid\"], [\"class\", \"stat-item fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"journey-section\", \"section\"], [1, \"section-title\"], [1, \"timeline\"], [\"class\", \"timeline-item fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"education-section\", \"section\"], [1, \"education-content\"], [1, \"education-card\", \"fade-in-up\"], [1, \"card-icon\"], [1, \"fas\", \"fa-graduation-cap\"], [1, \"education-card\", \"fade-in-up\", 2, \"animation-delay\", \"0.2s\"], [1, \"fas\", \"fa-target\"], [1, \"education-card\", \"fade-in-up\", 2, \"animation-delay\", \"0.4s\"], [1, \"fas\", \"fa-rocket\"], [1, \"stat-item\", \"fade-in-up\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"timeline-item\", \"fade-in-up\"], [1, \"timeline-marker\"], [1, \"timeline-content\"], [1, \"timeline-year\"]],\n        template: function AboutComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n            i0.ɵɵelement(5, \"i\", 5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 6)(7, \"h1\");\n            i0.ɵɵtext(8, \"About Me\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"h2\");\n            i0.ɵɵtext(10, \"Frontend Developer & Problem Solver\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"p\");\n            i0.ɵɵtext(12, \" Hello! I'm Abdullah G, a passionate frontend web developer who started my journey in 2024. I specialize in creating beautiful, responsive, and user-friendly web applications using modern technologies. \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"p\");\n            i0.ɵɵtext(14, \" My journey began with a curiosity about how websites work, and it quickly evolved into a passion for creating digital experiences that make a difference. I believe in writing clean, maintainable code and staying up-to-date with the latest industry trends and best practices. \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"p\");\n            i0.ɵɵtext(16, \" When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or planning my next big project. I'm always eager to learn and take on new challenges. \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(17, \"section\", 7)(18, \"div\", 1)(19, \"div\", 8);\n            i0.ɵɵtemplate(20, AboutComponent_div_20_Template, 5, 4, \"div\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(21, \"section\", 10)(22, \"div\", 1)(23, \"div\", 11)(24, \"h2\");\n            i0.ɵɵtext(25, \"My Journey\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"p\");\n            i0.ɵɵtext(27, \"From curiosity to expertise - here's how my development story unfolded\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"div\", 12);\n            i0.ɵɵtemplate(29, AboutComponent_div_29_Template, 9, 5, \"div\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(30, \"section\", 14)(31, \"div\", 1)(32, \"div\", 11)(33, \"h2\");\n            i0.ɵɵtext(34, \"Education & Goals\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"p\");\n            i0.ɵɵtext(36, \"Continuous learning and growth in the ever-evolving world of technology\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"div\", 15)(38, \"div\", 16)(39, \"div\", 17);\n            i0.ɵɵelement(40, \"i\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"h3\");\n            i0.ɵɵtext(42, \"Self-Taught Developer\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"p\");\n            i0.ɵɵtext(44, \" Dedicated to continuous learning through online courses, documentation, and hands-on projects. Focused on mastering modern web development technologies and best practices. \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(45, \"div\", 19)(46, \"div\", 17);\n            i0.ɵɵelement(47, \"i\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"h3\");\n            i0.ɵɵtext(49, \"Future Goals\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"p\");\n            i0.ɵɵtext(51, \" Aspiring to become a full-stack developer, contribute to meaningful open-source projects, and eventually start my own tech company to create innovative solutions. \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"div\", 21)(53, \"div\", 17);\n            i0.ɵɵelement(54, \"i\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"h3\");\n            i0.ɵɵtext(56, \"Startup Vision\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"p\");\n            i0.ɵɵtext(58, \" Started my entrepreneurial journey in 2024 with the vision of creating web solutions that solve real-world problems and make technology accessible to everyone. \");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(20);\n            i0.ɵɵproperty(\"ngForOf\", ctx.stats);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.timeline);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf],\n        styles: [\".about-hero[_ngcontent-%COMP%]{padding-top:120px}.about-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 2fr;gap:60px;align-items:center}.about-image[_ngcontent-%COMP%]{text-align:center}.about-image[_ngcontent-%COMP%]   .image-placeholder[_ngcontent-%COMP%]{width:300px;height:300px;border-radius:50%;background:linear-gradient(135deg,#667eea,#764ba2);display:flex;align-items:center;justify-content:center;margin:0 auto;box-shadow:0 20px 40px #667eea4d}.about-image[_ngcontent-%COMP%]   .image-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:120px;color:#fff}.about-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:3rem;font-weight:700;color:#2c3e50;margin-bottom:10px}.about-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.5rem;color:#667eea;margin-bottom:30px;font-weight:500}.about-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.1rem;line-height:1.8;color:#666;margin-bottom:20px}.stats-section[_ngcontent-%COMP%]{background:#f8f9fa;padding:80px 0}.stats-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:40px}.stat-item[_ngcontent-%COMP%]{text-align:center;padding:30px 20px;background:#fff;border-radius:15px;box-shadow:0 10px 30px #0000001a;transition:transform .3s ease}.stat-item[_ngcontent-%COMP%]:hover{transform:translateY(-5px)}.stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;background:linear-gradient(135deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;margin-bottom:10px}.stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:1rem;color:#666;font-weight:500}.journey-section[_ngcontent-%COMP%]{background:#fff}.timeline[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;position:relative}.timeline[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:50%;top:0;bottom:0;width:2px;background:linear-gradient(135deg,#667eea,#764ba2);transform:translate(-50%)}.timeline-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:50px;position:relative}.timeline-item[_ngcontent-%COMP%]:nth-child(odd){flex-direction:row-reverse}.timeline-item[_ngcontent-%COMP%]:nth-child(odd)   .timeline-content[_ngcontent-%COMP%]{text-align:right;margin-right:40px}.timeline-item[_ngcontent-%COMP%]:nth-child(2n)   .timeline-content[_ngcontent-%COMP%]{margin-left:40px}.timeline-marker[_ngcontent-%COMP%]{width:20px;height:20px;border-radius:50%;background:linear-gradient(135deg,#667eea,#764ba2);position:absolute;left:50%;transform:translate(-50%);z-index:2;box-shadow:0 0 0 4px #fff,0 0 0 8px #667eea}.timeline-content[_ngcontent-%COMP%]{flex:1;background:#fff;padding:30px;border-radius:15px;box-shadow:0 10px 30px #0000001a}.timeline-content[_ngcontent-%COMP%]   .timeline-year[_ngcontent-%COMP%]{font-size:.9rem;color:#667eea;font-weight:600;margin-bottom:10px}.timeline-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.3rem;color:#2c3e50;margin-bottom:15px}.timeline-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;line-height:1.6}.education-section[_ngcontent-%COMP%]{background:#f8f9fa}.education-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:40px}.education-card[_ngcontent-%COMP%]{background:#fff;padding:40px 30px;border-radius:15px;text-align:center;box-shadow:0 10px 30px #0000001a;transition:transform .3s ease}.education-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px)}.education-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;background:linear-gradient(135deg,#667eea,#764ba2);display:flex;align-items:center;justify-content:center;margin:0 auto 25px}.education-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;color:#fff}.education-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.4rem;color:#2c3e50;margin-bottom:20px}.education-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;line-height:1.6}@media (max-width: 768px){.about-content[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:40px;text-align:center}.about-image[_ngcontent-%COMP%]   .image-placeholder[_ngcontent-%COMP%]{width:200px;height:200px}.about-image[_ngcontent-%COMP%]   .image-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:80px}.about-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2.5rem}.timeline[_ngcontent-%COMP%]:before{left:20px}.timeline-item[_ngcontent-%COMP%]{flex-direction:column!important;align-items:flex-start}.timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]{margin-left:50px!important;margin-right:0!important;text-align:left!important}.timeline-marker[_ngcontent-%COMP%]{left:20px!important;transform:translate(-50%)}.stats-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);gap:20px}.education-content[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return AboutComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}