{"ast": null, "code": "import { raceInit } from '../observable/race';\nimport { operate } from '../util/lift';\nimport { identity } from '../util/identity';\nexport function raceWith(...otherSources) {\n  return !otherSources.length ? identity : operate((source, subscriber) => {\n    raceInit([source, ...otherSources])(subscriber);\n  });\n}\n//# sourceMappingURL=raceWith.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}