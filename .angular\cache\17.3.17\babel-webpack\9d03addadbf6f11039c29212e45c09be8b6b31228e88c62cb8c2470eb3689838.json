{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { routes } from './app/app.routes';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes), provideAnimations()]\n}).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "AppComponent", "provideRouter", "routes", "provideAnimations", "providers", "catch", "err", "console", "error"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { routes } from './app/app.routes';\nimport { provideAnimations } from '@angular/platform-browser/animations';\n\nbootstrapApplication(AppComponent, {\n  providers: [\n    provideRouter(routes),\n    provideAnimations()\n  ]\n}).catch(err => console.error(err));\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,MAAM,QAAQ,kBAAkB;AACzC,SAASC,iBAAiB,QAAQ,sCAAsC;AAExEJ,oBAAoB,CAACC,YAAY,EAAE;EACjCI,SAAS,EAAE,CACTH,aAAa,CAACC,MAAM,CAAC,EACrBC,iBAAiB,EAAE;CAEtB,CAAC,CAACE,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}