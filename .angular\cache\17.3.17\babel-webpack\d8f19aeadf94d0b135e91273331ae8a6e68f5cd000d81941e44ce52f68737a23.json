{"ast": null, "code": "export const intervalProvider = {\n  setInterval(handler, timeout, ...args) {\n    const {\n      delegate\n    } = intervalProvider;\n    if (delegate === null || delegate === void 0 ? void 0 : delegate.setInterval) {\n      return delegate.setInterval(handler, timeout, ...args);\n    }\n    return setInterval(handler, timeout, ...args);\n  },\n  clearInterval(handle) {\n    const {\n      delegate\n    } = intervalProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearInterval) || clearInterval)(handle);\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["intervalProvider", "setInterval", "handler", "timeout", "args", "delegate", "clearInterval", "handle", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/my folio/node_modules/rxjs/dist/esm/internal/scheduler/intervalProvider.js"], "sourcesContent": ["export const intervalProvider = {\n    setInterval(handler, timeout, ...args) {\n        const { delegate } = intervalProvider;\n        if (delegate === null || delegate === void 0 ? void 0 : delegate.setInterval) {\n            return delegate.setInterval(handler, timeout, ...args);\n        }\n        return setInterval(handler, timeout, ...args);\n    },\n    clearInterval(handle) {\n        const { delegate } = intervalProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearInterval) || clearInterval)(handle);\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB,GAAG;EAC5BC,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE,GAAGC,IAAI,EAAE;IACnC,MAAM;MAAEC;IAAS,CAAC,GAAGL,gBAAgB;IACrC,IAAIK,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACJ,WAAW,EAAE;MAC1E,OAAOI,QAAQ,CAACJ,WAAW,CAACC,OAAO,EAAEC,OAAO,EAAE,GAAGC,IAAI,CAAC;IAC1D;IACA,OAAOH,WAAW,CAACC,OAAO,EAAEC,OAAO,EAAE,GAAGC,IAAI,CAAC;EACjD,CAAC;EACDE,aAAaA,CAACC,MAAM,EAAE;IAClB,MAAM;MAAEF;IAAS,CAAC,GAAGL,gBAAgB;IACrC,OAAO,CAAC,CAACK,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,aAAa,KAAKA,aAAa,EAAEC,MAAM,CAAC;EAClH,CAAC;EACDF,QAAQ,EAAEG;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}