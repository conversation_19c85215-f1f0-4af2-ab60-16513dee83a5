{"ast": null, "code": "import { RouterOutlet } from '@angular/router';\nimport { NavbarComponent } from './components/navbar/navbar.component';\nimport { FooterComponent } from './components/footer/footer.component';\nimport * as i0 from \"@angular/core\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor() {\n      this.title = 'portfolio-website';\n    }\n    static {\n      this.ɵfac = function AppComponent_Factory(t) {\n        return new (t || AppComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: AppComponent,\n        selectors: [[\"app-root\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 4,\n        vars: 0,\n        template: function AppComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelement(0, \"app-navbar\");\n            i0.ɵɵelementStart(1, \"main\");\n            i0.ɵɵelement(2, \"router-outlet\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(3, \"app-footer\");\n          }\n        },\n        dependencies: [RouterOutlet, NavbarComponent, FooterComponent],\n        styles: [\"main[_ngcontent-%COMP%]{min-height:calc(100vh - 140px)}\"]\n      });\n    }\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}