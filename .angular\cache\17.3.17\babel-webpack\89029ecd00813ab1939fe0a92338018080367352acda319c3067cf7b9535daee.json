{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = () => ({\n  exact: true\n});\nexport let NavbarComponent = /*#__PURE__*/(() => {\n  class NavbarComponent {\n    constructor() {\n      this.isMenuOpen = false;\n      this.isScrolled = false;\n    }\n    ngOnInit() {\n      window.addEventListener('scroll', () => {\n        this.isScrolled = window.scrollY > 50;\n      });\n    }\n    toggleMenu() {\n      this.isMenuOpen = !this.isMenuOpen;\n    }\n    closeMenu() {\n      this.isMenuOpen = false;\n    }\n    static {\n      this.ɵfac = function NavbarComponent_Factory(t) {\n        return new (t || NavbarComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NavbarComponent,\n        selectors: [[\"app-navbar\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 44,\n        vars: 14,\n        consts: [[1, \"navbar\"], [1, \"container\"], [1, \"nav-content\"], [1, \"logo\"], [\"routerLink\", \"/\", 3, \"click\"], [1, \"logo-text\"], [1, \"nav-links\", \"desktop-nav\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 3, \"routerLinkActiveOptions\"], [\"routerLink\", \"/about\", \"routerLinkActive\", \"active\"], [\"routerLink\", \"/skills\", \"routerLinkActive\", \"active\"], [\"routerLink\", \"/projects\", \"routerLinkActive\", \"active\"], [\"routerLink\", \"/contact\", \"routerLinkActive\", \"active\"], [1, \"mobile-menu-btn\", 3, \"click\"], [1, \"mobile-nav\"], [1, \"nav-links\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 3, \"click\", \"routerLinkActiveOptions\"], [\"routerLink\", \"/about\", \"routerLinkActive\", \"active\", 3, \"click\"], [\"routerLink\", \"/skills\", \"routerLinkActive\", \"active\", 3, \"click\"], [\"routerLink\", \"/projects\", \"routerLinkActive\", \"active\", 3, \"click\"], [\"routerLink\", \"/contact\", \"routerLinkActive\", \"active\", 3, \"click\"]],\n        template: function NavbarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"nav\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4);\n            i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_4_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵelementStart(5, \"span\", 5);\n            i0.ɵɵtext(6, \"Abdullah G\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(7, \"ul\", 6)(8, \"li\")(9, \"a\", 7);\n            i0.ɵɵtext(10, \"Home\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(11, \"li\")(12, \"a\", 8);\n            i0.ɵɵtext(13, \"About\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"li\")(15, \"a\", 9);\n            i0.ɵɵtext(16, \"Skills\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(17, \"li\")(18, \"a\", 10);\n            i0.ɵɵtext(19, \"Projects\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"li\")(21, \"a\", 11);\n            i0.ɵɵtext(22, \"Contact\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(23, \"div\", 12);\n            i0.ɵɵlistener(\"click\", function NavbarComponent_Template_div_click_23_listener() {\n              return ctx.toggleMenu();\n            });\n            i0.ɵɵelement(24, \"span\")(25, \"span\")(26, \"span\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(27, \"div\", 13)(28, \"ul\", 14)(29, \"li\")(30, \"a\", 15);\n            i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_30_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵtext(31, \"Home\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"li\")(33, \"a\", 16);\n            i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_33_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵtext(34, \"About\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(35, \"li\")(36, \"a\", 17);\n            i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_36_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵtext(37, \"Skills\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(38, \"li\")(39, \"a\", 18);\n            i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_39_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵtext(40, \"Projects\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(41, \"li\")(42, \"a\", 19);\n            i0.ɵɵlistener(\"click\", function NavbarComponent_Template_a_click_42_listener() {\n              return ctx.closeMenu();\n            });\n            i0.ɵɵtext(43, \"Contact\");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"scrolled\", ctx.isScrolled);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(12, _c0));\n            i0.ɵɵadvance(15);\n            i0.ɵɵclassProp(\"active\", ctx.isMenuOpen);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"active\", ctx.isMenuOpen);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"active\", ctx.isMenuOpen);\n            i0.ɵɵadvance();\n            i0.ɵɵclassProp(\"active\", ctx.isMenuOpen);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(13, _c0));\n          }\n        },\n        dependencies: [CommonModule, RouterModule, i1.RouterLink, i1.RouterLinkActive],\n        styles: [\".navbar[_ngcontent-%COMP%]{position:fixed;top:0;left:0;right:0;background:#fffffff2;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);z-index:1000;transition:all .3s ease;padding:15px 0}.navbar.scrolled[_ngcontent-%COMP%]{background:#fffffffa;box-shadow:0 2px 20px #0000001a;padding:10px 0}.nav-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%]{font-size:1.8rem;font-weight:700;background:linear-gradient(135deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;text-decoration:none}.desktop-nav[_ngcontent-%COMP%]{display:flex;list-style:none;gap:30px}.desktop-nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none;color:#333;font-weight:500;transition:all .3s ease;position:relative}.desktop-nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .desktop-nav[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%]{color:#667eea}.desktop-nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:-5px;left:0;width:0;height:2px;background:linear-gradient(135deg,#667eea,#764ba2);transition:width .3s ease}.desktop-nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:after, .desktop-nav[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%]:after{width:100%}.mobile-menu-btn[_ngcontent-%COMP%]{display:none;flex-direction:column;cursor:pointer;padding:5px}.mobile-menu-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:25px;height:3px;background:#333;margin:3px 0;transition:all .3s ease;border-radius:2px}.mobile-menu-btn[_ngcontent-%COMP%]   span.active[_ngcontent-%COMP%]:nth-child(1){transform:rotate(45deg) translate(6px,6px)}.mobile-menu-btn[_ngcontent-%COMP%]   span.active[_ngcontent-%COMP%]:nth-child(2){opacity:0}.mobile-menu-btn[_ngcontent-%COMP%]   span.active[_ngcontent-%COMP%]:nth-child(3){transform:rotate(-45deg) translate(6px,-6px)}.mobile-nav[_ngcontent-%COMP%]{display:none;position:absolute;top:100%;left:0;right:0;background:#fff;box-shadow:0 5px 20px #0000001a;transform:translateY(-20px);opacity:0;visibility:hidden;transition:all .3s ease}.mobile-nav.active[_ngcontent-%COMP%]{display:block;transform:translateY(0);opacity:1;visibility:visible}.mobile-nav[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]{list-style:none;padding:20px 0}.mobile-nav[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:0}.mobile-nav[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:block;padding:15px 20px;text-decoration:none;color:#333;font-weight:500;transition:all .3s ease}.mobile-nav[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .mobile-nav[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%]{background:#f8f9fa;color:#667eea}@media (max-width: 768px){.desktop-nav[_ngcontent-%COMP%]{display:none}.mobile-menu-btn[_ngcontent-%COMP%]{display:flex}.mobile-nav[_ngcontent-%COMP%]{display:block}}\"]\n      });\n    }\n  }\n  return NavbarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}