{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport let FooterComponent = /*#__PURE__*/(() => {\n  class FooterComponent {\n    constructor() {\n      this.currentYear = new Date().getFullYear();\n    }\n    static {\n      this.ɵfac = function FooterComponent_Factory(t) {\n        return new (t || FooterComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FooterComponent,\n        selectors: [[\"app-footer\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 13,\n        vars: 1,\n        consts: [[1, \"footer\"], [1, \"container\"], [1, \"footer-content\"], [1, \"footer-text\"], [1, \"social-links\"], [\"href\", \"https://github.com\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\"], [1, \"fab\", \"fa-github\"], [\"href\", \"https://linkedin.com\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\"], [1, \"fab\", \"fa-linkedin\"], [\"href\", \"mailto:<EMAIL>\"], [1, \"fas\", \"fa-envelope\"]],\n        template: function FooterComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"footer\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"p\");\n            i0.ɵɵtext(5);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(6, \"div\", 4)(7, \"a\", 5);\n            i0.ɵɵelement(8, \"i\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"a\", 7);\n            i0.ɵɵelement(10, \"i\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"a\", 9);\n            i0.ɵɵelement(12, \"i\", 10);\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\"\\u00A9 \", ctx.currentYear, \" Abdullah G | All Rights Reserved\");\n          }\n        },\n        dependencies: [CommonModule],\n        styles: [\".footer[_ngcontent-%COMP%]{background:#2c3e50;color:#fff;padding:30px 0;margin-top:auto}.footer-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:20px}.footer-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:.9rem;color:#bdc3c7}.social-links[_ngcontent-%COMP%]{display:flex;gap:15px}.social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:40px;height:40px;background:#ffffff1a;color:#fff;border-radius:50%;text-decoration:none;transition:all .3s ease}.social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{background:#667eea;transform:translateY(-2px)}.social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1rem}@media (max-width: 768px){.footer-content[_ngcontent-%COMP%]{flex-direction:column;text-align:center}}\"]\n      });\n    }\n  }\n  return FooterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}