{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nfunction ContactComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"i\", 20);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"Message Sent Successfully!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Thank you for reaching out. I'll get back to you soon.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ContactComponent_form_17_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"name\"), \" \");\n  }\n}\nfunction ContactComponent_form_17_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"email\"), \" \");\n  }\n}\nfunction ContactComponent_form_17_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"subject\"), \" \");\n  }\n}\nfunction ContactComponent_form_17_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"message\"), \" \");\n  }\n}\nfunction ContactComponent_form_17_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵtext(2, \" Send Message \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactComponent_form_17_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2, \" Sending... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactComponent_form_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 21);\n    i0.ɵɵlistener(\"ngSubmit\", function ContactComponent_form_17_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 22)(2, \"div\", 23)(3, \"label\", 24);\n    i0.ɵɵtext(4, \"Full Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 25);\n    i0.ɵɵtemplate(6, ContactComponent_form_17_div_6_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 23)(8, \"label\", 27);\n    i0.ɵɵtext(9, \"Email Address *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 28);\n    i0.ɵɵtemplate(11, ContactComponent_form_17_div_11_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 23)(13, \"label\", 29);\n    i0.ɵɵtext(14, \"Subject *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 30);\n    i0.ɵɵtemplate(16, ContactComponent_form_17_div_16_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 23)(18, \"label\", 31);\n    i0.ɵɵtext(19, \"Message *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"textarea\", 32);\n    i0.ɵɵtemplate(21, ContactComponent_form_17_div_21_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 33);\n    i0.ɵɵtemplate(23, ContactComponent_form_17_span_23_Template, 3, 0, \"span\", 34)(24, ContactComponent_form_17_span_24_Template, 3, 0, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.contactForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"error\", ctx_r1.f[\"name\"].invalid && ctx_r1.f[\"name\"].touched);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"name\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"error\", ctx_r1.f[\"email\"].invalid && ctx_r1.f[\"email\"].touched);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"email\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"error\", ctx_r1.f[\"subject\"].invalid && ctx_r1.f[\"subject\"].touched);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"subject\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"error\", ctx_r1.f[\"message\"].invalid && ctx_r1.f[\"message\"].touched);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"message\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n  }\n}\nfunction ContactComponent_div_25_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 42);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"href\", info_r3.link, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r3.value);\n  }\n}\nfunction ContactComponent_div_25_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r3.value);\n  }\n}\nfunction ContactComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ContactComponent_div_25_a_6_Template, 2, 2, \"a\", 41)(7, ContactComponent_div_25_span_7_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const info_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(info_r3.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(info_r3.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", info_r3.link !== \"#\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", info_r3.link === \"#\");\n  }\n}\nfunction ContactComponent_a_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 43);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const social_r4 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background-color\", social_r4.color);\n    i0.ɵɵproperty(\"href\", social_r4.url, i0.ɵɵsanitizeUrl)(\"title\", social_r4.name);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(social_r4.icon);\n  }\n}\nexport class ContactComponent {\n  constructor(fb) {\n    this.fb = fb;\n    this.isSubmitting = false;\n    this.isSubmitted = false;\n    this.contactInfo = [{\n      icon: 'fas fa-envelope',\n      title: 'Email',\n      value: '<EMAIL>',\n      link: 'mailto:<EMAIL>'\n    }, {\n      icon: 'fas fa-phone',\n      title: 'Phone',\n      value: '+****************',\n      link: 'tel:+15551234567'\n    }, {\n      icon: 'fas fa-map-marker-alt',\n      title: 'Location',\n      value: 'Your City, Country',\n      link: '#'\n    }];\n    this.socialLinks = [{\n      icon: 'fab fa-github',\n      name: 'GitHub',\n      url: 'https://github.com',\n      color: '#333'\n    }, {\n      icon: 'fab fa-linkedin',\n      name: 'LinkedIn',\n      url: 'https://linkedin.com',\n      color: '#0077b5'\n    }, {\n      icon: 'fab fa-twitter',\n      name: 'Twitter',\n      url: 'https://twitter.com',\n      color: '#1da1f2'\n    }, {\n      icon: 'fab fa-instagram',\n      name: 'Instagram',\n      url: 'https://instagram.com',\n      color: '#e4405f'\n    }];\n    this.contactForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      subject: ['', [Validators.required, Validators.minLength(5)]],\n      message: ['', [Validators.required, Validators.minLength(10)]]\n    });\n  }\n  get f() {\n    return this.contactForm.controls;\n  }\n  onSubmit() {\n    if (this.contactForm.valid) {\n      this.isSubmitting = true;\n      // Simulate form submission\n      setTimeout(() => {\n        this.isSubmitting = false;\n        this.isSubmitted = true;\n        this.contactForm.reset();\n        // Reset success message after 5 seconds\n        setTimeout(() => {\n          this.isSubmitted = false;\n        }, 5000);\n      }, 2000);\n    } else {\n      // Mark all fields as touched to show validation errors\n      Object.keys(this.contactForm.controls).forEach(key => {\n        this.contactForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n  getFieldError(fieldName) {\n    const field = this.contactForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n      }\n      if (field.errors['email']) {\n        return 'Please enter a valid email address';\n      }\n      if (field.errors['minlength']) {\n        const requiredLength = field.errors['minlength'].requiredLength;\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${requiredLength} characters`;\n      }\n    }\n    return '';\n  }\n  openLink(url) {\n    if (url !== '#') {\n      window.open(url, '_blank');\n    }\n  }\n  static {\n    this.ɵfac = function ContactComponent_Factory(t) {\n      return new (t || ContactComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContactComponent,\n      selectors: [[\"app-contact\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 38,\n      vars: 4,\n      consts: [[1, \"contact-hero\", \"section\"], [1, \"container\"], [1, \"section-title\"], [1, \"contact-content\", \"section\"], [1, \"contact-grid\"], [1, \"contact-form-section\", \"fade-in-up\"], [1, \"form-header\"], [\"class\", \"success-message\", 4, \"ngIf\"], [\"class\", \"contact-form\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"contact-info-section\", \"fade-in-up\", 2, \"animation-delay\", \"0.2s\"], [1, \"info-header\"], [1, \"contact-methods\"], [\"class\", \"contact-method\", 4, \"ngFor\", \"ngForOf\"], [1, \"social-section\"], [1, \"social-links\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", \"class\", \"social-link\", 3, \"href\", \"background-color\", \"title\", 4, \"ngFor\", \"ngForOf\"], [1, \"availability\"], [1, \"availability-status\"], [1, \"status-indicator\", \"available\"], [1, \"success-message\"], [1, \"fas\", \"fa-check-circle\"], [1, \"contact-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-row\"], [1, \"form-group\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", \"placeholder\", \"Your full name\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"for\", \"subject\"], [\"type\", \"text\", \"id\", \"subject\", \"formControlName\", \"subject\", \"placeholder\", \"What's this about?\"], [\"for\", \"message\"], [\"id\", \"message\", \"formControlName\", \"message\", \"rows\", \"6\", \"placeholder\", \"Tell me about your project or just say hello...\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"submit-btn\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"error-message\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"contact-method\"], [1, \"method-icon\"], [1, \"method-content\"], [3, \"href\", 4, \"ngIf\"], [3, \"href\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"social-link\", 3, \"href\", \"title\"]],\n      template: function ContactComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4, \"Get In Touch\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Let's discuss your next project or just say hello!\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"section\", 3)(8, \"div\", 1)(9, \"div\", 4)(10, \"div\", 5)(11, \"div\", 6)(12, \"h2\");\n          i0.ɵɵtext(13, \"Send Me a Message\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p\");\n          i0.ɵɵtext(15, \"Fill out the form below and I'll get back to you as soon as possible.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(16, ContactComponent_div_16_Template, 6, 0, \"div\", 7)(17, ContactComponent_form_17_Template, 25, 16, \"form\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"h2\");\n          i0.ɵɵtext(21, \"Let's Connect\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"Feel free to reach out through any of these channels.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 11);\n          i0.ɵɵtemplate(25, ContactComponent_div_25_Template, 8, 5, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 13)(27, \"h3\");\n          i0.ɵɵtext(28, \"Follow Me\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 14);\n          i0.ɵɵtemplate(30, ContactComponent_a_30_Template, 2, 6, \"a\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 16)(32, \"div\", 17);\n          i0.ɵɵelement(33, \"div\", 18);\n          i0.ɵɵelementStart(34, \"span\");\n          i0.ɵɵtext(35, \"Available for new projects\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"p\");\n          i0.ɵɵtext(37, \"I'm currently accepting new freelance projects and full-time opportunities.\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmitted);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitted);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.contactInfo);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.socialLinks);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\".contact-hero[_ngcontent-%COMP%] {\\n  padding-top: 120px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n\\n.contact-content[_ngcontent-%COMP%] {\\n  background: white;\\n}\\n\\n.contact-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 60px;\\n  align-items: start;\\n}\\n\\n.contact-form-section[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.contact-form-section[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n.contact-form-section[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1.1rem;\\n}\\n\\n.success-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  border-radius: 15px;\\n  margin-bottom: 30px;\\n}\\n.success-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin-bottom: 20px;\\n}\\n.success-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin-bottom: 10px;\\n}\\n.success-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  opacity: 0.9;\\n}\\n\\n.contact-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n}\\n.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n  color: #2c3e50;\\n}\\n.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px;\\n  border: 2px solid #e9ecef;\\n  border-radius: 10px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n  font-family: inherit;\\n}\\n.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #667eea;\\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\\n}\\n.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input.error[_ngcontent-%COMP%], .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea.error[_ngcontent-%COMP%] {\\n  border-color: #e74c3c;\\n}\\n.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder {\\n  color: #adb5bd;\\n}\\n.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  resize: vertical;\\n  min-height: 120px;\\n}\\n.contact-form[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  color: #e74c3c;\\n  font-size: 0.875rem;\\n  margin-top: 5px;\\n}\\n.contact-form[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n.contact-form[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n}\\n.contact-form[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.contact-info-section[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.contact-info-section[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n.contact-info-section[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\n.contact-methods[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n\\n.contact-method[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 20px;\\n  background: #f8f9fa;\\n  border-radius: 10px;\\n  margin-bottom: 15px;\\n  transition: all 0.3s ease;\\n}\\n.contact-method[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  transform: translateX(5px);\\n}\\n.contact-method[_ngcontent-%COMP%]   .method-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 20px;\\n}\\n.contact-method[_ngcontent-%COMP%]   .method-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 1.2rem;\\n}\\n.contact-method[_ngcontent-%COMP%]   .method-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 5px;\\n  font-size: 1.1rem;\\n}\\n.contact-method[_ngcontent-%COMP%]   .method-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%], .contact-method[_ngcontent-%COMP%]   .method-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n.contact-method[_ngcontent-%COMP%]   .method-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .contact-method[_ngcontent-%COMP%]   .method-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.social-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.social-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  margin-bottom: 20px;\\n  font-size: 1.3rem;\\n}\\n.social-section[_ngcontent-%COMP%]   .social-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.social-section[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n}\\n.social-section[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\\n}\\n.social-section[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n\\n.availability[_ngcontent-%COMP%] {\\n  padding: 25px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  border-radius: 15px;\\n}\\n.availability[_ngcontent-%COMP%]   .availability-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n.availability[_ngcontent-%COMP%]   .availability-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n  border-radius: 50%;\\n  margin-right: 10px;\\n}\\n.availability[_ngcontent-%COMP%]   .availability-status[_ngcontent-%COMP%]   .status-indicator.available[_ngcontent-%COMP%] {\\n  background: #2ecc71;\\n  box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.3);\\n}\\n.availability[_ngcontent-%COMP%]   .availability-status[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.availability[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  opacity: 0.9;\\n  margin: 0;\\n  line-height: 1.5;\\n}\\n\\n@media (max-width: 768px) {\\n  .contact-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 40px;\\n  }\\n  .contact-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 0;\\n  }\\n  .social-links[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .contact-method[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .contact-method[_ngcontent-%COMP%]   .method-icon[_ngcontent-%COMP%] {\\n    margin-right: 0;\\n    margin-bottom: 15px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .contact-form-section[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%], .contact-form-section[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%], .contact-info-section[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%], .contact-info-section[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "getFieldError", "ɵɵlistener", "ContactComponent_form_17_Template_form_ngSubmit_0_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "ɵɵtemplate", "ContactComponent_form_17_div_6_Template", "ContactComponent_form_17_div_11_Template", "ContactComponent_form_17_div_16_Template", "ContactComponent_form_17_div_21_Template", "ContactComponent_form_17_span_23_Template", "ContactComponent_form_17_span_24_Template", "ɵɵproperty", "contactForm", "ɵɵclassProp", "f", "invalid", "touched", "isSubmitting", "info_r3", "link", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "value", "ContactComponent_div_25_a_6_Template", "ContactComponent_div_25_span_7_Template", "ɵɵclassMap", "icon", "title", "ɵɵstyleProp", "social_r4", "color", "url", "name", "ContactComponent", "constructor", "fb", "isSubmitted", "contactInfo", "socialLinks", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "subject", "message", "controls", "valid", "setTimeout", "reset", "Object", "keys", "for<PERSON>ach", "key", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "field", "errors", "char<PERSON>t", "toUpperCase", "slice", "<PERSON><PERSON><PERSON><PERSON>", "openLink", "window", "open", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ContactComponent_Template", "rf", "ctx", "ContactComponent_div_16_Template", "ContactComponent_form_17_Template", "ContactComponent_div_25_Template", "ContactComponent_a_30_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "FormGroupDirective", "FormControlName", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\pages\\contact\\contact.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\pages\\contact\\contact.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\n\n@Component({\n  selector: 'app-contact',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule],\n  templateUrl: './contact.component.html',\n  styleUrls: ['./contact.component.scss']\n})\nexport class ContactComponent {\n  contactForm: FormGroup;\n  isSubmitting = false;\n  isSubmitted = false;\n\n  contactInfo = [\n    {\n      icon: 'fas fa-envelope',\n      title: 'Email',\n      value: '<EMAIL>',\n      link: 'mailto:<EMAIL>'\n    },\n    {\n      icon: 'fas fa-phone',\n      title: 'Phone',\n      value: '+****************',\n      link: 'tel:+15551234567'\n    },\n    {\n      icon: 'fas fa-map-marker-alt',\n      title: 'Location',\n      value: 'Your City, Country',\n      link: '#'\n    }\n  ];\n\n  socialLinks = [\n    {\n      icon: 'fab fa-github',\n      name: 'Git<PERSON><PERSON>',\n      url: 'https://github.com',\n      color: '#333'\n    },\n    {\n      icon: 'fab fa-linkedin',\n      name: 'LinkedIn',\n      url: 'https://linkedin.com',\n      color: '#0077b5'\n    },\n    {\n      icon: 'fab fa-twitter',\n      name: 'Twitter',\n      url: 'https://twitter.com',\n      color: '#1da1f2'\n    },\n    {\n      icon: 'fab fa-instagram',\n      name: 'Instagram',\n      url: 'https://instagram.com',\n      color: '#e4405f'\n    }\n  ];\n\n  constructor(private fb: FormBuilder) {\n    this.contactForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      subject: ['', [Validators.required, Validators.minLength(5)]],\n      message: ['', [Validators.required, Validators.minLength(10)]]\n    });\n  }\n\n  get f() {\n    return this.contactForm.controls;\n  }\n\n  onSubmit() {\n    if (this.contactForm.valid) {\n      this.isSubmitting = true;\n      \n      // Simulate form submission\n      setTimeout(() => {\n        this.isSubmitting = false;\n        this.isSubmitted = true;\n        this.contactForm.reset();\n        \n        // Reset success message after 5 seconds\n        setTimeout(() => {\n          this.isSubmitted = false;\n        }, 5000);\n      }, 2000);\n    } else {\n      // Mark all fields as touched to show validation errors\n      Object.keys(this.contactForm.controls).forEach(key => {\n        this.contactForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.contactForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n      }\n      if (field.errors['email']) {\n        return 'Please enter a valid email address';\n      }\n      if (field.errors['minlength']) {\n        const requiredLength = field.errors['minlength'].requiredLength;\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${requiredLength} characters`;\n      }\n    }\n    return '';\n  }\n\n  openLink(url: string) {\n    if (url !== '#') {\n      window.open(url, '_blank');\n    }\n  }\n}\n", "<section class=\"contact-hero section\">\n  <div class=\"container\">\n    <div class=\"section-title\">\n      <h1>Get In Touch</h1>\n      <p>Let's discuss your next project or just say hello!</p>\n    </div>\n  </div>\n</section>\n\n<section class=\"contact-content section\">\n  <div class=\"container\">\n    <div class=\"contact-grid\">\n      <!-- Contact Form -->\n      <div class=\"contact-form-section fade-in-up\">\n        <div class=\"form-header\">\n          <h2>Send Me a Message</h2>\n          <p>Fill out the form below and I'll get back to you as soon as possible.</p>\n        </div>\n\n        <!-- Success Message -->\n        <div class=\"success-message\" *ngIf=\"isSubmitted\">\n          <i class=\"fas fa-check-circle\"></i>\n          <h3>Message Sent Successfully!</h3>\n          <p>Thank you for reaching out. I'll get back to you soon.</p>\n        </div>\n\n        <!-- Contact Form -->\n        <form [formGroup]=\"contactForm\" (ngSubmit)=\"onSubmit()\" class=\"contact-form\" *ngIf=\"!isSubmitted\">\n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label for=\"name\">Full Name *</label>\n              <input \n                type=\"text\" \n                id=\"name\" \n                formControlName=\"name\"\n                [class.error]=\"f['name'].invalid && f['name'].touched\"\n                placeholder=\"Your full name\">\n              <div class=\"error-message\" *ngIf=\"getFieldError('name')\">\n                {{ getFieldError('name') }}\n              </div>\n            </div>\n\n            <div class=\"form-group\">\n              <label for=\"email\">Email Address *</label>\n              <input \n                type=\"email\" \n                id=\"email\" \n                formControlName=\"email\"\n                [class.error]=\"f['email'].invalid && f['email'].touched\"\n                placeholder=\"<EMAIL>\">\n              <div class=\"error-message\" *ngIf=\"getFieldError('email')\">\n                {{ getFieldError('email') }}\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"subject\">Subject *</label>\n            <input \n              type=\"text\" \n              id=\"subject\" \n              formControlName=\"subject\"\n              [class.error]=\"f['subject'].invalid && f['subject'].touched\"\n              placeholder=\"What's this about?\">\n            <div class=\"error-message\" *ngIf=\"getFieldError('subject')\">\n              {{ getFieldError('subject') }}\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"message\">Message *</label>\n            <textarea \n              id=\"message\" \n              formControlName=\"message\"\n              [class.error]=\"f['message'].invalid && f['message'].touched\"\n              rows=\"6\" \n              placeholder=\"Tell me about your project or just say hello...\"></textarea>\n            <div class=\"error-message\" *ngIf=\"getFieldError('message')\">\n              {{ getFieldError('message') }}\n            </div>\n          </div>\n\n          <button \n            type=\"submit\" \n            class=\"btn btn-primary submit-btn\"\n            [disabled]=\"isSubmitting\">\n            <span *ngIf=\"!isSubmitting\">\n              <i class=\"fas fa-paper-plane\"></i> Send Message\n            </span>\n            <span *ngIf=\"isSubmitting\">\n              <i class=\"fas fa-spinner fa-spin\"></i> Sending...\n            </span>\n          </button>\n        </form>\n      </div>\n\n      <!-- Contact Info -->\n      <div class=\"contact-info-section fade-in-up\" style=\"animation-delay: 0.2s;\">\n        <div class=\"info-header\">\n          <h2>Let's Connect</h2>\n          <p>Feel free to reach out through any of these channels.</p>\n        </div>\n\n        <div class=\"contact-methods\">\n          <div class=\"contact-method\" *ngFor=\"let info of contactInfo\">\n            <div class=\"method-icon\">\n              <i [class]=\"info.icon\"></i>\n            </div>\n            <div class=\"method-content\">\n              <h4>{{ info.title }}</h4>\n              <a [href]=\"info.link\" *ngIf=\"info.link !== '#'\">{{ info.value }}</a>\n              <span *ngIf=\"info.link === '#'\">{{ info.value }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"social-section\">\n          <h3>Follow Me</h3>\n          <div class=\"social-links\">\n            <a \n              *ngFor=\"let social of socialLinks\" \n              [href]=\"social.url\" \n              target=\"_blank\" \n              rel=\"noopener noreferrer\"\n              class=\"social-link\"\n              [style.background-color]=\"social.color\"\n              [title]=\"social.name\">\n              <i [class]=\"social.icon\"></i>\n            </a>\n          </div>\n        </div>\n\n        <div class=\"availability\">\n          <div class=\"availability-status\">\n            <div class=\"status-indicator available\"></div>\n            <span>Available for new projects</span>\n          </div>\n          <p>I'm currently accepting new freelance projects and full-time opportunities.</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</section>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;;;;;;ICkBhFC,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAE,SAAA,YAAmC;IACnCF,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,6DAAsD;IAC3DH,EAD2D,CAAAI,YAAA,EAAI,EACzD;;;;;IAaAJ,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,aAAA,cACF;;;;;IAWAR,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,aAAA,eACF;;;;;IAYFR,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,aAAA,iBACF;;;;;IAWAR,EAAA,CAAAC,cAAA,cAA4D;IAC1DD,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,aAAA,iBACF;;;;;IAOAR,EAAA,CAAAC,cAAA,WAA4B;IAC1BD,EAAA,CAAAE,SAAA,YAAkC;IAACF,EAAA,CAAAG,MAAA,qBACrC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACPJ,EAAA,CAAAC,cAAA,WAA2B;IACzBD,EAAA,CAAAE,SAAA,YAAsC;IAACF,EAAA,CAAAG,MAAA,mBACzC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAhEXJ,EAAA,CAAAC,cAAA,eAAkG;IAAlED,EAAA,CAAAS,UAAA,sBAAAC,2DAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAYP,MAAA,CAAAQ,QAAA,EAAU;IAAA,EAAC;IAGjDf,EAFJ,CAAAC,cAAA,cAAsB,cACI,gBACJ;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACrCJ,EAAA,CAAAE,SAAA,gBAK+B;IAC/BF,EAAA,CAAAgB,UAAA,IAAAC,uCAAA,kBAAyD;IAG3DjB,EAAA,CAAAI,YAAA,EAAM;IAGJJ,EADF,CAAAC,cAAA,cAAwB,gBACH;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1CJ,EAAA,CAAAE,SAAA,iBAKuC;IACvCF,EAAA,CAAAgB,UAAA,KAAAE,wCAAA,kBAA0D;IAI9DlB,EADE,CAAAI,YAAA,EAAM,EACF;IAGJJ,EADF,CAAAC,cAAA,eAAwB,iBACD;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACtCJ,EAAA,CAAAE,SAAA,iBAKmC;IACnCF,EAAA,CAAAgB,UAAA,KAAAG,wCAAA,kBAA4D;IAG9DnB,EAAA,CAAAI,YAAA,EAAM;IAGJJ,EADF,CAAAC,cAAA,eAAwB,iBACD;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACtCJ,EAAA,CAAAE,SAAA,oBAK2E;IAC3EF,EAAA,CAAAgB,UAAA,KAAAI,wCAAA,kBAA4D;IAG9DpB,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,kBAG4B;IAI1BD,EAHA,CAAAgB,UAAA,KAAAK,yCAAA,mBAA4B,KAAAC,yCAAA,mBAGD;IAI/BtB,EADE,CAAAI,YAAA,EAAS,EACJ;;;;IAlEDJ,EAAA,CAAAuB,UAAA,cAAAhB,MAAA,CAAAiB,WAAA,CAAyB;IAQvBxB,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAyB,WAAA,UAAAlB,MAAA,CAAAmB,CAAA,SAAAC,OAAA,IAAApB,MAAA,CAAAmB,CAAA,SAAAE,OAAA,CAAsD;IAE5B5B,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAuB,UAAA,SAAAhB,MAAA,CAAAC,aAAA,SAA2B;IAWrDR,EAAA,CAAAK,SAAA,GAAwD;IAAxDL,EAAA,CAAAyB,WAAA,UAAAlB,MAAA,CAAAmB,CAAA,UAAAC,OAAA,IAAApB,MAAA,CAAAmB,CAAA,UAAAE,OAAA,CAAwD;IAE9B5B,EAAA,CAAAK,SAAA,EAA4B;IAA5BL,EAAA,CAAAuB,UAAA,SAAAhB,MAAA,CAAAC,aAAA,UAA4B;IAYxDR,EAAA,CAAAK,SAAA,GAA4D;IAA5DL,EAAA,CAAAyB,WAAA,UAAAlB,MAAA,CAAAmB,CAAA,YAAAC,OAAA,IAAApB,MAAA,CAAAmB,CAAA,YAAAE,OAAA,CAA4D;IAElC5B,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAuB,UAAA,SAAAhB,MAAA,CAAAC,aAAA,YAA8B;IAUxDR,EAAA,CAAAK,SAAA,GAA4D;IAA5DL,EAAA,CAAAyB,WAAA,UAAAlB,MAAA,CAAAmB,CAAA,YAAAC,OAAA,IAAApB,MAAA,CAAAmB,CAAA,YAAAE,OAAA,CAA4D;IAGlC5B,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAuB,UAAA,SAAAhB,MAAA,CAAAC,aAAA,YAA8B;IAQ1DR,EAAA,CAAAK,SAAA,EAAyB;IAAzBL,EAAA,CAAAuB,UAAA,aAAAhB,MAAA,CAAAsB,YAAA,CAAyB;IAClB7B,EAAA,CAAAK,SAAA,EAAmB;IAAnBL,EAAA,CAAAuB,UAAA,UAAAhB,MAAA,CAAAsB,YAAA,CAAmB;IAGnB7B,EAAA,CAAAK,SAAA,EAAkB;IAAlBL,EAAA,CAAAuB,UAAA,SAAAhB,MAAA,CAAAsB,YAAA,CAAkB;;;;;IAqBvB7B,EAAA,CAAAC,cAAA,YAAgD;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAjEJ,EAAA,CAAAuB,UAAA,SAAAO,OAAA,CAAAC,IAAA,EAAA/B,EAAA,CAAAgC,aAAA,CAAkB;IAA2BhC,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAiC,iBAAA,CAAAH,OAAA,CAAAI,KAAA,CAAgB;;;;;IAChElC,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAAvBJ,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAiC,iBAAA,CAAAH,OAAA,CAAAI,KAAA,CAAgB;;;;;IANlDlC,EADF,CAAAC,cAAA,cAA6D,cAClC;IACvBD,EAAA,CAAAE,SAAA,QAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,cAA4B,SACtB;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEzBJ,EADA,CAAAgB,UAAA,IAAAmB,oCAAA,gBAAgD,IAAAC,uCAAA,mBAChB;IAEpCpC,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAPCJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAqC,UAAA,CAAAP,OAAA,CAAAQ,IAAA,CAAmB;IAGlBtC,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAiC,iBAAA,CAAAH,OAAA,CAAAS,KAAA,CAAgB;IACGvC,EAAA,CAAAK,SAAA,EAAuB;IAAvBL,EAAA,CAAAuB,UAAA,SAAAO,OAAA,CAAAC,IAAA,SAAuB;IACvC/B,EAAA,CAAAK,SAAA,EAAuB;IAAvBL,EAAA,CAAAuB,UAAA,SAAAO,OAAA,CAAAC,IAAA,SAAuB;;;;;IAQhC/B,EAAA,CAAAC,cAAA,YAOwB;IACtBD,EAAA,CAAAE,SAAA,QAA6B;IAC/BF,EAAA,CAAAI,YAAA,EAAI;;;;IAHFJ,EAAA,CAAAwC,WAAA,qBAAAC,SAAA,CAAAC,KAAA,CAAuC;IACvC1C,EALA,CAAAuB,UAAA,SAAAkB,SAAA,CAAAE,GAAA,EAAA3C,EAAA,CAAAgC,aAAA,CAAmB,UAAAS,SAAA,CAAAG,IAAA,CAKE;IAClB5C,EAAA,CAAAK,SAAA,EAAqB;IAArBL,EAAA,CAAAqC,UAAA,CAAAI,SAAA,CAAAH,IAAA,CAAqB;;;ADpHtC,OAAM,MAAOO,gBAAgB;EAqD3BC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAnDtB,KAAAlB,YAAY,GAAG,KAAK;IACpB,KAAAmB,WAAW,GAAG,KAAK;IAEnB,KAAAC,WAAW,GAAG,CACZ;MACEX,IAAI,EAAE,iBAAiB;MACvBC,KAAK,EAAE,OAAO;MACdL,KAAK,EAAE,sBAAsB;MAC7BH,IAAI,EAAE;KACP,EACD;MACEO,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,OAAO;MACdL,KAAK,EAAE,mBAAmB;MAC1BH,IAAI,EAAE;KACP,EACD;MACEO,IAAI,EAAE,uBAAuB;MAC7BC,KAAK,EAAE,UAAU;MACjBL,KAAK,EAAE,oBAAoB;MAC3BH,IAAI,EAAE;KACP,CACF;IAED,KAAAmB,WAAW,GAAG,CACZ;MACEZ,IAAI,EAAE,eAAe;MACrBM,IAAI,EAAE,QAAQ;MACdD,GAAG,EAAE,oBAAoB;MACzBD,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,iBAAiB;MACvBM,IAAI,EAAE,UAAU;MAChBD,GAAG,EAAE,sBAAsB;MAC3BD,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,gBAAgB;MACtBM,IAAI,EAAE,SAAS;MACfD,GAAG,EAAE,qBAAqB;MAC1BD,KAAK,EAAE;KACR,EACD;MACEJ,IAAI,EAAE,kBAAkB;MACxBM,IAAI,EAAE,WAAW;MACjBD,GAAG,EAAE,uBAAuB;MAC5BD,KAAK,EAAE;KACR,CACF;IAGC,IAAI,CAAClB,WAAW,GAAG,IAAI,CAACuB,EAAE,CAACI,KAAK,CAAC;MAC/BP,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC7C,UAAU,CAACqD,QAAQ,EAAErD,UAAU,CAACsD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACvD,UAAU,CAACqD,QAAQ,EAAErD,UAAU,CAACuD,KAAK,CAAC,CAAC;MACpDC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACxD,UAAU,CAACqD,QAAQ,EAAErD,UAAU,CAACsD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7DG,OAAO,EAAE,CAAC,EAAE,EAAE,CAACzD,UAAU,CAACqD,QAAQ,EAAErD,UAAU,CAACsD,SAAS,CAAC,EAAE,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEA,IAAI3B,CAACA,CAAA;IACH,OAAO,IAAI,CAACF,WAAW,CAACiC,QAAQ;EAClC;EAEA1C,QAAQA,CAAA;IACN,IAAI,IAAI,CAACS,WAAW,CAACkC,KAAK,EAAE;MAC1B,IAAI,CAAC7B,YAAY,GAAG,IAAI;MAExB;MACA8B,UAAU,CAAC,MAAK;QACd,IAAI,CAAC9B,YAAY,GAAG,KAAK;QACzB,IAAI,CAACmB,WAAW,GAAG,IAAI;QACvB,IAAI,CAACxB,WAAW,CAACoC,KAAK,EAAE;QAExB;QACAD,UAAU,CAAC,MAAK;UACd,IAAI,CAACX,WAAW,GAAG,KAAK;QAC1B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,IAAI,CAAC;KACT,MAAM;MACL;MACAa,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtC,WAAW,CAACiC,QAAQ,CAAC,CAACM,OAAO,CAACC,GAAG,IAAG;QACnD,IAAI,CAACxC,WAAW,CAACyC,GAAG,CAACD,GAAG,CAAC,EAAEE,aAAa,EAAE;MAC5C,CAAC,CAAC;;EAEN;EAEA1D,aAAaA,CAAC2D,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC5C,WAAW,CAACyC,GAAG,CAACE,SAAS,CAAC;IAC7C,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACxC,OAAO,EAAE;MAClC,IAAIwC,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,GAAGF,SAAS,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGJ,SAAS,CAACK,KAAK,CAAC,CAAC,CAAC,cAAc;;MAEhF,IAAIJ,KAAK,CAACC,MAAM,CAAC,OAAO,CAAC,EAAE;QACzB,OAAO,oCAAoC;;MAE7C,IAAID,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE;QAC7B,MAAMI,cAAc,GAAGL,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,CAACI,cAAc;QAC/D,OAAO,GAAGN,SAAS,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE,GAAGJ,SAAS,CAACK,KAAK,CAAC,CAAC,CAAC,qBAAqBC,cAAc,aAAa;;;IAGpH,OAAO,EAAE;EACX;EAEAC,QAAQA,CAAC/B,GAAW;IAClB,IAAIA,GAAG,KAAK,GAAG,EAAE;MACfgC,MAAM,CAACC,IAAI,CAACjC,GAAG,EAAE,QAAQ,CAAC;;EAE9B;;;uBA9GWE,gBAAgB,EAAA7C,EAAA,CAAA6E,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAhBlC,gBAAgB;MAAAmC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAlF,EAAA,CAAAmF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRvBzF,EAHN,CAAAC,cAAA,iBAAsC,aACb,aACM,SACrB;UAAAD,EAAA,CAAAG,MAAA,mBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACrBJ,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAG,MAAA,yDAAkD;UAG3DH,EAH2D,CAAAI,YAAA,EAAI,EACrD,EACF,EACE;UAQAJ,EANV,CAAAC,cAAA,iBAAyC,aAChB,aACK,cAEqB,cAClB,UACnB;UAAAD,EAAA,CAAAG,MAAA,yBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC1BJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,6EAAqE;UAC1EH,EAD0E,CAAAI,YAAA,EAAI,EACxE;UAUNJ,EAPA,CAAAgB,UAAA,KAAA2E,gCAAA,iBAAiD,KAAAC,iCAAA,oBAOiD;UAmEpG5F,EAAA,CAAAI,YAAA,EAAM;UAKFJ,EAFJ,CAAAC,cAAA,cAA4E,eACjD,UACnB;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACtBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,6DAAqD;UAC1DH,EAD0D,CAAAI,YAAA,EAAI,EACxD;UAENJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAgB,UAAA,KAAA6E,gCAAA,kBAA6D;UAU/D7F,EAAA,CAAAI,YAAA,EAAM;UAGJJ,EADF,CAAAC,cAAA,eAA4B,UACtB;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAC,cAAA,eAA0B;UACxBD,EAAA,CAAAgB,UAAA,KAAA8E,8BAAA,gBAOwB;UAI5B9F,EADE,CAAAI,YAAA,EAAM,EACF;UAGJJ,EADF,CAAAC,cAAA,eAA0B,eACS;UAC/BD,EAAA,CAAAE,SAAA,eAA8C;UAC9CF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAG,MAAA,kCAA0B;UAClCH,EADkC,CAAAI,YAAA,EAAO,EACnC;UACNJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,mFAA2E;UAKxFH,EALwF,CAAAI,YAAA,EAAI,EAC9E,EACF,EACF,EACF,EACE;;;UA1H4BJ,EAAA,CAAAK,SAAA,IAAiB;UAAjBL,EAAA,CAAAuB,UAAA,SAAAmE,GAAA,CAAA1C,WAAA,CAAiB;UAO+BhD,EAAA,CAAAK,SAAA,EAAkB;UAAlBL,EAAA,CAAAuB,UAAA,UAAAmE,GAAA,CAAA1C,WAAA,CAAkB;UA6EjDhD,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAuB,UAAA,YAAAmE,GAAA,CAAAzC,WAAA,CAAc;UAgBpCjD,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAuB,UAAA,YAAAmE,GAAA,CAAAxC,WAAA,CAAc;;;qBDjHnCrD,YAAY,EAAAkG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEnG,mBAAmB,EAAAgF,EAAA,CAAAoB,aAAA,EAAApB,EAAA,CAAAqB,oBAAA,EAAArB,EAAA,CAAAsB,eAAA,EAAAtB,EAAA,CAAAuB,oBAAA,EAAAvB,EAAA,CAAAwB,kBAAA,EAAAxB,EAAA,CAAAyB,eAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}