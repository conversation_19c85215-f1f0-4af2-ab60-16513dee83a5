{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nfunction ProjectsComponent_div_15_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tech_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tech_r4);\n  }\n}\nfunction ProjectsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16);\n    i0.ɵɵelement(2, \"img\", 17);\n    i0.ɵɵelementStart(3, \"div\", 18)(4, \"div\", 19)(5, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_div_15_Template_button_click_5_listener() {\n      const project_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openDemo(project_r2.demoUrl));\n    });\n    i0.ɵɵelement(6, \"i\", 21);\n    i0.ɵɵtext(7, \" Live Demo \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_div_15_Template_button_click_8_listener() {\n      const project_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openGithub(project_r2.githubUrl));\n    });\n    i0.ɵɵelement(9, \"i\", 23);\n    i0.ɵɵtext(10, \" GitHub \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"h3\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 25);\n    i0.ɵɵtemplate(17, ProjectsComponent_div_15_span_17_Template, 2, 1, \"span\", 26);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const project_r2 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r5 * 0.2 + \"s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", project_r2.image, i0.ɵɵsanitizeUrl)(\"alt\", project_r2.title);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(project_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r2.longDescription);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", project_r2.technologies);\n  }\n}\nfunction ProjectsComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_button_24_Template_button_click_0_listener() {\n      const category_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectCategory(category_r7.id));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r2.selectedCategory === category_r7.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", category_r7.name, \" \");\n  }\n}\nfunction ProjectsComponent_div_26_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tech_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tech_r10);\n  }\n}\nfunction ProjectsComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30);\n    i0.ɵɵelement(2, \"img\", 17);\n    i0.ɵɵelementStart(3, \"div\", 31)(4, \"div\", 32)(5, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_div_26_Template_button_click_5_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openDemo(project_r9.demoUrl));\n    });\n    i0.ɵɵelement(6, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_div_26_Template_button_click_7_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openGithub(project_r9.githubUrl));\n    });\n    i0.ɵɵelement(8, \"i\", 23);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"div\", 35)(10, \"h3\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 36);\n    i0.ɵɵtemplate(15, ProjectsComponent_div_26_span_15_Template, 2, 1, \"span\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 38)(17, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ProjectsComponent_div_26_Template_button_click_17_listener() {\n      const project_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openDemo(project_r9.demoUrl));\n    });\n    i0.ɵɵtext(18, \" View Project \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const project_r9 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r11 * 0.1 + \"s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", project_r9.image, i0.ɵɵsanitizeUrl)(\"alt\", project_r9.title);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(project_r9.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(project_r9.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", project_r9.technologies);\n  }\n}\nfunction ProjectsComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"i\", 42);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"No projects found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"No projects match the selected category.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ProjectsComponent {\n  constructor() {\n    this.selectedCategory = 'all';\n    this.categories = [{\n      id: 'all',\n      name: 'All Projects'\n    }, {\n      id: 'web',\n      name: 'Web Apps'\n    }, {\n      id: 'angular',\n      name: 'Angular'\n    }, {\n      id: 'responsive',\n      name: 'Responsive'\n    }];\n    this.projects = [{\n      id: 1,\n      title: 'E-Commerce Dashboard',\n      description: 'Modern admin dashboard for e-commerce management with real-time analytics.',\n      longDescription: 'A comprehensive admin dashboard built with Angular featuring real-time sales analytics, inventory management, and customer insights. Includes responsive design and interactive charts.',\n      image: 'https://via.placeholder.com/400x250/667eea/ffffff?text=E-Commerce+Dashboard',\n      technologies: ['Angular', 'TypeScript', 'SCSS', 'Chart.js'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: true,\n      category: 'angular'\n    }, {\n      id: 2,\n      title: 'Task Management App',\n      description: 'Collaborative task management application with drag-and-drop functionality.',\n      longDescription: 'A Kanban-style task management application that allows teams to organize projects, assign tasks, and track progress with an intuitive drag-and-drop interface.',\n      image: 'https://via.placeholder.com/400x250/764ba2/ffffff?text=Task+Manager',\n      technologies: ['Angular', 'RxJS', 'Angular CDK', 'SCSS'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: true,\n      category: 'web'\n    }, {\n      id: 3,\n      title: 'Weather App',\n      description: 'Beautiful weather application with location-based forecasts and animations.',\n      longDescription: 'A responsive weather application that provides current weather conditions and 7-day forecasts with smooth animations and beautiful UI design.',\n      image: 'https://via.placeholder.com/400x250/4ecdc4/ffffff?text=Weather+App',\n      technologies: ['JavaScript', 'CSS3', 'Weather API', 'HTML5'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: false,\n      category: 'web'\n    }, {\n      id: 4,\n      title: 'Portfolio Website',\n      description: 'Personal portfolio website showcasing projects and skills.',\n      longDescription: 'This very portfolio website built with Angular, featuring responsive design, smooth animations, and modern UI components.',\n      image: 'https://via.placeholder.com/400x250/667eea/ffffff?text=Portfolio+Site',\n      technologies: ['Angular', 'TypeScript', 'SCSS', 'Responsive Design'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: true,\n      category: 'responsive'\n    }, {\n      id: 5,\n      title: 'Restaurant Landing Page',\n      description: 'Elegant landing page for a restaurant with online reservation system.',\n      longDescription: 'A beautiful, responsive landing page for a restaurant featuring menu showcase, online reservations, and contact information with smooth scrolling effects.',\n      image: 'https://via.placeholder.com/400x250/ff6b6b/ffffff?text=Restaurant+Site',\n      technologies: ['HTML5', 'CSS3', 'JavaScript', 'Responsive Design'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: false,\n      category: 'responsive'\n    }, {\n      id: 6,\n      title: 'Blog Platform',\n      description: 'Modern blog platform with content management and user authentication.',\n      longDescription: 'A full-featured blog platform with user authentication, content management, commenting system, and responsive design.',\n      image: 'https://via.placeholder.com/400x250/45b7d1/ffffff?text=Blog+Platform',\n      technologies: ['Angular', 'TypeScript', 'SCSS', 'Firebase'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: false,\n      category: 'angular'\n    }];\n  }\n  get filteredProjects() {\n    if (this.selectedCategory === 'all') {\n      return this.projects;\n    }\n    return this.projects.filter(project => project.category === this.selectedCategory);\n  }\n  get featuredProjects() {\n    return this.projects.filter(project => project.featured);\n  }\n  selectCategory(categoryId) {\n    this.selectedCategory = categoryId;\n  }\n  openDemo(url) {\n    if (url !== '#') {\n      window.open(url, '_blank');\n    }\n  }\n  openGithub(url) {\n    if (url !== '#') {\n      window.open(url, '_blank');\n    }\n  }\n  static {\n    this.ɵfac = function ProjectsComponent_Factory(t) {\n      return new (t || ProjectsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProjectsComponent,\n      selectors: [[\"app-projects\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 37,\n      vars: 4,\n      consts: [[1, \"projects-hero\", \"section\"], [1, \"container\"], [1, \"section-title\"], [1, \"featured-projects\", \"section\"], [1, \"featured-grid\"], [\"class\", \"featured-project fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"all-projects\", \"section\"], [1, \"category-filter\"], [\"class\", \"filter-btn\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"projects-grid\"], [\"class\", \"project-card fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"cta-section\", \"section\"], [1, \"cta-content\"], [\"routerLink\", \"/contact\", 1, \"btn\", \"btn-primary\"], [1, \"featured-project\", \"fade-in-up\"], [1, \"project-image\"], [3, \"src\", \"alt\"], [1, \"project-overlay\"], [1, \"project-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"btn\", \"btn-outline\", 3, \"click\"], [1, \"fab\", \"fa-github\"], [1, \"project-content\"], [1, \"project-tech\"], [\"class\", \"tech-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"tech-tag\"], [1, \"filter-btn\", 3, \"click\"], [1, \"project-card\", \"fade-in-up\"], [1, \"card-image\"], [1, \"card-overlay\"], [1, \"card-actions\"], [\"title\", \"Live Demo\", 1, \"action-btn\", 3, \"click\"], [\"title\", \"GitHub\", 1, \"action-btn\", 3, \"click\"], [1, \"card-content\"], [1, \"card-tech\"], [\"class\", \"tech-badge\", 4, \"ngFor\", \"ngForOf\"], [1, \"card-footer\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"tech-badge\"], [1, \"empty-state\"], [1, \"fas\", \"fa-folder-open\"]],\n      template: function ProjectsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4, \"My Projects\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"A showcase of my work and the technologies I've mastered\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"section\", 3)(8, \"div\", 1)(9, \"div\", 2)(10, \"h2\");\n          i0.ɵɵtext(11, \"Featured Projects\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\");\n          i0.ɵɵtext(13, \"Highlighting my best work and most challenging projects\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 4);\n          i0.ɵɵtemplate(15, ProjectsComponent_div_15_Template, 18, 7, \"div\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"section\", 6)(17, \"div\", 1)(18, \"div\", 2)(19, \"h2\");\n          i0.ɵɵtext(20, \"All Projects\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\");\n          i0.ɵɵtext(22, \"Browse through all my projects by category\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 7);\n          i0.ɵɵtemplate(24, ProjectsComponent_button_24_Template, 2, 3, \"button\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 9);\n          i0.ɵɵtemplate(26, ProjectsComponent_div_26_Template, 19, 7, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, ProjectsComponent_div_27_Template, 6, 0, \"div\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"section\", 12)(29, \"div\", 1)(30, \"div\", 13)(31, \"h2\");\n          i0.ɵɵtext(32, \"Interested in Working Together?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"p\");\n          i0.ɵɵtext(34, \"I'm always open to discussing new opportunities and exciting projects.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"a\", 14);\n          i0.ɵɵtext(36, \"Get In Touch\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngForOf\", ctx.featuredProjects);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredProjects);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredProjects.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, i2.RouterLink],\n      styles: [\".projects-hero[_ngcontent-%COMP%] {\\n  padding-top: 120px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n\\n.featured-projects[_ngcontent-%COMP%] {\\n  background: white;\\n}\\n\\n.featured-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));\\n  gap: 40px;\\n}\\n\\n.featured-project[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.3s ease;\\n}\\n.featured-project[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n\\n.project-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 250px;\\n  overflow: hidden;\\n}\\n.project-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.project-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.project-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.8);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.project-image[_ngcontent-%COMP%]:hover   .project-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.project-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n\\n.project-content[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n.project-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #2c3e50;\\n  margin-bottom: 15px;\\n}\\n.project-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.6;\\n  margin-bottom: 20px;\\n}\\n\\n.project-tech[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 10px;\\n}\\n\\n.tech-tag[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 5px 12px;\\n  border-radius: 20px;\\n  font-size: 0.85rem;\\n  font-weight: 500;\\n}\\n\\n.all-projects[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n}\\n\\n.category-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  flex-wrap: wrap;\\n  gap: 15px;\\n  margin-bottom: 50px;\\n}\\n\\n.filter-btn[_ngcontent-%COMP%] {\\n  padding: 10px 25px;\\n  border: 2px solid #667eea;\\n  background: transparent;\\n  color: #667eea;\\n  border-radius: 25px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.filter-btn[_ngcontent-%COMP%]:hover, .filter-btn.active[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n}\\n\\n.projects-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 30px;\\n}\\n\\n.project-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n}\\n.project-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);\\n}\\n\\n.card-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 200px;\\n  overflow: hidden;\\n}\\n.card-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.card-image[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.card-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0, 0, 0, 0.7);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.card-image[_ngcontent-%COMP%]:hover   .card-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.card-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  border: 2px solid white;\\n  background: transparent;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  color: #333;\\n}\\n\\n.card-content[_ngcontent-%COMP%] {\\n  padding: 25px;\\n}\\n.card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n.card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.6;\\n  margin-bottom: 20px;\\n  font-size: 0.95rem;\\n}\\n\\n.card-tech[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n  margin-bottom: 20px;\\n}\\n\\n.tech-badge[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  color: #667eea;\\n  padding: 4px 10px;\\n  border-radius: 15px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n  border: 1px solid #e9ecef;\\n}\\n\\n.card-footer[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  font-size: 0.9rem;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n  color: #666;\\n}\\n.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  margin-bottom: 20px;\\n  opacity: 0.5;\\n}\\n.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin-bottom: 10px;\\n  color: #2c3e50;\\n}\\n\\n.cta-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  text-align: center;\\n}\\n\\n.cta-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  margin-bottom: 20px;\\n}\\n.cta-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  margin-bottom: 30px;\\n  opacity: 0.9;\\n}\\n.cta-content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  background: white;\\n  color: #667eea;\\n}\\n.cta-content[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n  transform: translateY(-2px);\\n}\\n\\n@media (max-width: 768px) {\\n  .featured-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .projects-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .category-filter[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n  .project-actions[_ngcontent-%COMP%], .card-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 10px;\\n  }\\n  .cta-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .project-content[_ngcontent-%COMP%], .card-content[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .featured-project[_ngcontent-%COMP%], .project-card[_ngcontent-%COMP%] {\\n    margin: 0 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcHJvamVjdHMvcHJvamVjdHMuY29tcG9uZW50LnNjc3MiLCJ3ZWJwYWNrOi8vLi8uLi8uLi8uLi8uLi9MYXB0b3AlMjBIb3VzZS9PbmVEcml2ZS9EZXNrdG9wL215JTIwZm9saW8vc3JjL2FwcC9wYWdlcy9wcm9qZWN0cy9wcm9qZWN0cy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGtCQUFBO0VBQ0EsNkRBQUE7QUNDRjs7QURFQTtFQUNFLGlCQUFBO0FDQ0Y7O0FERUE7RUFDRSxhQUFBO0VBQ0EsMkRBQUE7RUFDQSxTQUFBO0FDQ0Y7O0FERUE7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSwwQ0FBQTtFQUNBLCtCQUFBO0FDQ0Y7QURDRTtFQUNFLDJCQUFBO0FDQ0o7O0FER0E7RUFDRSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxnQkFBQTtBQ0FGO0FERUU7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlCQUFBO0VBQ0EsK0JBQUE7QUNBSjtBREdFO0VBQ0Usc0JBQUE7QUNESjs7QURLQTtFQUNFLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLE9BQUE7RUFDQSxRQUFBO0VBQ0EsU0FBQTtFQUNBLDhCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxVQUFBO0VBQ0EsNkJBQUE7QUNGRjtBRElFO0VBQ0UsVUFBQTtBQ0ZKOztBRE1BO0VBQ0UsYUFBQTtFQUNBLFNBQUE7QUNIRjs7QURNQTtFQUNFLGFBQUE7QUNIRjtBREtFO0VBQ0UsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7QUNISjtBRE1FO0VBQ0UsV0FBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7QUNKSjs7QURRQTtFQUNFLGFBQUE7RUFDQSxlQUFBO0VBQ0EsU0FBQTtBQ0xGOztBRFFBO0VBQ0UsNkRBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUNMRjs7QURRQTtFQUNFLG1CQUFBO0FDTEY7O0FEUUE7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxlQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FDTEY7O0FEUUE7RUFDRSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsdUJBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtBQ0xGO0FET0U7RUFFRSxtQkFBQTtFQUNBLFlBQUE7QUNOSjs7QURVQTtFQUNFLGFBQUE7RUFDQSwyREFBQTtFQUNBLFNBQUE7QUNQRjs7QURVQTtFQUNFLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLDBDQUFBO0VBQ0EseUJBQUE7QUNQRjtBRFNFO0VBQ0UsMkJBQUE7RUFDQSwyQ0FBQTtBQ1BKOztBRFdBO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7QUNSRjtBRFVFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLCtCQUFBO0FDUko7QURXRTtFQUNFLHNCQUFBO0FDVEo7O0FEYUE7RUFDRSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSw4QkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsVUFBQTtFQUNBLDZCQUFBO0FDVkY7QURZRTtFQUNFLFVBQUE7QUNWSjs7QURjQTtFQUNFLGFBQUE7RUFDQSxTQUFBO0FDWEY7O0FEY0E7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsdUJBQUE7RUFDQSx1QkFBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtBQ1hGO0FEYUU7RUFDRSxpQkFBQTtFQUNBLFdBQUE7QUNYSjs7QURlQTtFQUNFLGFBQUE7QUNaRjtBRGNFO0VBQ0UsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7QUNaSjtBRGVFO0VBQ0UsV0FBQTtFQUNBLGdCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtBQ2JKOztBRGlCQTtFQUNFLGFBQUE7RUFDQSxlQUFBO0VBQ0EsUUFBQTtFQUNBLG1CQUFBO0FDZEY7O0FEaUJBO0VBQ0UsbUJBQUE7RUFDQSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtBQ2RGOztBRGtCRTtFQUNFLGlCQUFBO0VBQ0EsaUJBQUE7QUNmSjs7QURtQkE7RUFDRSxrQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtBQ2hCRjtBRGtCRTtFQUNFLGVBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7QUNoQko7QURtQkU7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQTtBQ2pCSjs7QURxQkE7RUFDRSw2REFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtBQ2xCRjs7QURzQkU7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0FDbkJKO0FEc0JFO0VBQ0UsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7QUNwQko7QUR1QkU7RUFDRSxpQkFBQTtFQUNBLGNBQUE7QUNyQko7QUR1Qkk7RUFDRSxtQkFBQTtFQUNBLDJCQUFBO0FDckJOOztBRDBCQTtFQUNFO0lBQ0UsMEJBQUE7RUN2QkY7RUQwQkE7SUFDRSwwQkFBQTtFQ3hCRjtFRDJCQTtJQUNFLHNCQUFBO0lBQ0EsbUJBQUE7RUN6QkY7RUQ0QkE7O0lBRUUsc0JBQUE7SUFDQSxTQUFBO0VDMUJGO0VENkJBO0lBQ0UsZUFBQTtFQzNCRjtBQUNGO0FEOEJBO0VBQ0U7O0lBRUUsYUFBQTtFQzVCRjtFRCtCQTs7SUFFRSxjQUFBO0VDN0JGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIucHJvamVjdHMtaGVybyB7XG4gIHBhZGRpbmctdG9wOiAxMjBweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y4ZjlmYSAwJSwgI2U5ZWNlZiAxMDAlKTtcbn1cblxuLmZlYXR1cmVkLXByb2plY3RzIHtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG59XG5cbi5mZWF0dXJlZC1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCg1MDBweCwgMWZyKSk7XG4gIGdhcDogNDBweDtcbn1cblxuLmZlYXR1cmVkLXByb2plY3Qge1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgYm9yZGVyLXJhZGl1czogMTVweDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgYm94LXNoYWRvdzogMCAxMHB4IDMwcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xuXG4gICY6aG92ZXIge1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcbiAgfVxufVxuXG4ucHJvamVjdC1pbWFnZSB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgaGVpZ2h0OiAyNTBweDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcblxuICBpbWcge1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIGhlaWdodDogMTAwJTtcbiAgICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xuICB9XG5cbiAgJjpob3ZlciBpbWcge1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XG4gIH1cbn1cblxuLnByb2plY3Qtb3ZlcmxheSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgYm90dG9tOiAwO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuOCk7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBvcGFjaXR5OiAwO1xuICB0cmFuc2l0aW9uOiBvcGFjaXR5IDAuM3MgZWFzZTtcblxuICAucHJvamVjdC1pbWFnZTpob3ZlciAmIHtcbiAgICBvcGFjaXR5OiAxO1xuICB9XG59XG5cbi5wcm9qZWN0LWFjdGlvbnMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDE1cHg7XG59XG5cbi5wcm9qZWN0LWNvbnRlbnQge1xuICBwYWRkaW5nOiAzMHB4O1xuXG4gIGgzIHtcbiAgICBmb250LXNpemU6IDEuNXJlbTtcbiAgICBjb2xvcjogIzJjM2U1MDtcbiAgICBtYXJnaW4tYm90dG9tOiAxNXB4O1xuICB9XG5cbiAgcCB7XG4gICAgY29sb3I6ICM2NjY7XG4gICAgbGluZS1oZWlnaHQ6IDEuNjtcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICB9XG59XG5cbi5wcm9qZWN0LXRlY2gge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LXdyYXA6IHdyYXA7XG4gIGdhcDogMTBweDtcbn1cblxuLnRlY2gtdGFnIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcbiAgY29sb3I6IHdoaXRlO1xuICBwYWRkaW5nOiA1cHggMTJweDtcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgZm9udC1zaXplOiAwLjg1cmVtO1xuICBmb250LXdlaWdodDogNTAwO1xufVxuXG4uYWxsLXByb2plY3RzIHtcbiAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbn1cblxuLmNhdGVnb3J5LWZpbHRlciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBmbGV4LXdyYXA6IHdyYXA7XG4gIGdhcDogMTVweDtcbiAgbWFyZ2luLWJvdHRvbTogNTBweDtcbn1cblxuLmZpbHRlci1idG4ge1xuICBwYWRkaW5nOiAxMHB4IDI1cHg7XG4gIGJvcmRlcjogMnB4IHNvbGlkICM2NjdlZWE7XG4gIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICBjb2xvcjogIzY2N2VlYTtcbiAgYm9yZGVyLXJhZGl1czogMjVweDtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuXG4gICY6aG92ZXIsXG4gICYuYWN0aXZlIHtcbiAgICBiYWNrZ3JvdW5kOiAjNjY3ZWVhO1xuICAgIGNvbG9yOiB3aGl0ZTtcbiAgfVxufVxuXG4ucHJvamVjdHMtZ3JpZCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMzUwcHgsIDFmcikpO1xuICBnYXA6IDMwcHg7XG59XG5cbi5wcm9qZWN0LWNhcmQge1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgYm9yZGVyLXJhZGl1czogMTVweDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgYm94LXNoYWRvdzogMCA1cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMDgpO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuXG4gICY6aG92ZXIge1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcbiAgICBib3gtc2hhZG93OiAwIDE1cHggMzBweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xuICB9XG59XG5cbi5jYXJkLWltYWdlIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBoZWlnaHQ6IDIwMHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuXG4gIGltZyB7XG4gICAgd2lkdGg6IDEwMCU7XG4gICAgaGVpZ2h0OiAxMDAlO1xuICAgIG9iamVjdC1maXQ6IGNvdmVyO1xuICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7XG4gIH1cblxuICAmOmhvdmVyIGltZyB7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcbiAgfVxufVxuXG4uY2FyZC1vdmVybGF5IHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICBib3R0b206IDA7XG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC43KTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIG9wYWNpdHk6IDA7XG4gIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zcyBlYXNlO1xuXG4gIC5jYXJkLWltYWdlOmhvdmVyICYge1xuICAgIG9wYWNpdHk6IDE7XG4gIH1cbn1cblxuLmNhcmQtYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMTVweDtcbn1cblxuLmFjdGlvbi1idG4ge1xuICB3aWR0aDogNTBweDtcbiAgaGVpZ2h0OiA1MHB4O1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIGJvcmRlcjogMnB4IHNvbGlkIHdoaXRlO1xuICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgY29sb3I6IHdoaXRlO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuXG4gICY6aG92ZXIge1xuICAgIGJhY2tncm91bmQ6IHdoaXRlO1xuICAgIGNvbG9yOiAjMzMzO1xuICB9XG59XG5cbi5jYXJkLWNvbnRlbnQge1xuICBwYWRkaW5nOiAyNXB4O1xuXG4gIGgzIHtcbiAgICBmb250LXNpemU6IDEuM3JlbTtcbiAgICBjb2xvcjogIzJjM2U1MDtcbiAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuICB9XG5cbiAgcCB7XG4gICAgY29sb3I6ICM2NjY7XG4gICAgbGluZS1oZWlnaHQ6IDEuNjtcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcbiAgfVxufVxuXG4uY2FyZC10ZWNoIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC13cmFwOiB3cmFwO1xuICBnYXA6IDhweDtcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcbn1cblxuLnRlY2gtYmFkZ2Uge1xuICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICBjb2xvcjogIzY2N2VlYTtcbiAgcGFkZGluZzogNHB4IDEwcHg7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBmb250LXdlaWdodDogNTAwO1xuICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmO1xufVxuXG4uY2FyZC1mb290ZXIge1xuICAuYnRuLXNtIHtcbiAgICBwYWRkaW5nOiA4cHggMjBweDtcbiAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgfVxufVxuXG4uZW1wdHktc3RhdGUge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIHBhZGRpbmc6IDYwcHggMjBweDtcbiAgY29sb3I6ICM2NjY7XG5cbiAgaSB7XG4gICAgZm9udC1zaXplOiA0cmVtO1xuICAgIG1hcmdpbi1ib3R0b206IDIwcHg7XG4gICAgb3BhY2l0eTogMC41O1xuICB9XG5cbiAgaDMge1xuICAgIGZvbnQtc2l6ZTogMS41cmVtO1xuICAgIG1hcmdpbi1ib3R0b206IDEwcHg7XG4gICAgY29sb3I6ICMyYzNlNTA7XG4gIH1cbn1cblxuLmN0YS1zZWN0aW9uIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcbiAgY29sb3I6IHdoaXRlO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi5jdGEtY29udGVudCB7XG4gIGgyIHtcbiAgICBmb250LXNpemU6IDIuNXJlbTtcbiAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICB9XG5cbiAgcCB7XG4gICAgZm9udC1zaXplOiAxLjJyZW07XG4gICAgbWFyZ2luLWJvdHRvbTogMzBweDtcbiAgICBvcGFjaXR5OiAwLjk7XG4gIH1cblxuICAuYnRuIHtcbiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICBjb2xvcjogIzY2N2VlYTtcblxuICAgICY6aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbiAgICB9XG4gIH1cbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5mZWF0dXJlZC1ncmlkIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgfVxuXG4gIC5wcm9qZWN0cy1ncmlkIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgfVxuXG4gIC5jYXRlZ29yeS1maWx0ZXIge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgfVxuXG4gIC5wcm9qZWN0LWFjdGlvbnMsXG4gIC5jYXJkLWFjdGlvbnMge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiAxMHB4O1xuICB9XG5cbiAgLmN0YS1jb250ZW50IGgyIHtcbiAgICBmb250LXNpemU6IDJyZW07XG4gIH1cbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDQ4MHB4KSB7XG4gIC5wcm9qZWN0LWNvbnRlbnQsXG4gIC5jYXJkLWNvbnRlbnQge1xuICAgIHBhZGRpbmc6IDIwcHg7XG4gIH1cblxuICAuZmVhdHVyZWQtcHJvamVjdCxcbiAgLnByb2plY3QtY2FyZCB7XG4gICAgbWFyZ2luOiAwIDEwcHg7XG4gIH1cbn1cbiIsIi5wcm9qZWN0cy1oZXJvIHtcbiAgcGFkZGluZy10b3A6IDEyMHB4O1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmOWZhIDAlLCAjZTllY2VmIDEwMCUpO1xufVxuXG4uZmVhdHVyZWQtcHJvamVjdHMge1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbn1cblxuLmZlYXR1cmVkLWdyaWQge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDUwMHB4LCAxZnIpKTtcbiAgZ2FwOiA0MHB4O1xufVxuXG4uZmVhdHVyZWQtcHJvamVjdCB7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAxNXB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBib3gtc2hhZG93OiAwIDEwcHggMzBweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7XG59XG4uZmVhdHVyZWQtcHJvamVjdDpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcbn1cblxuLnByb2plY3QtaW1hZ2Uge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGhlaWdodDogMjUwcHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG4ucHJvamVjdC1pbWFnZSBpbWcge1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xuICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3MgZWFzZTtcbn1cbi5wcm9qZWN0LWltYWdlOmhvdmVyIGltZyB7XG4gIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XG59XG5cbi5wcm9qZWN0LW92ZXJsYXkge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMDtcbiAgbGVmdDogMDtcbiAgcmlnaHQ6IDA7XG4gIGJvdHRvbTogMDtcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjgpO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgb3BhY2l0eTogMDtcbiAgdHJhbnNpdGlvbjogb3BhY2l0eSAwLjNzIGVhc2U7XG59XG4ucHJvamVjdC1pbWFnZTpob3ZlciAucHJvamVjdC1vdmVybGF5IHtcbiAgb3BhY2l0eTogMTtcbn1cblxuLnByb2plY3QtYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMTVweDtcbn1cblxuLnByb2plY3QtY29udGVudCB7XG4gIHBhZGRpbmc6IDMwcHg7XG59XG4ucHJvamVjdC1jb250ZW50IGgzIHtcbiAgZm9udC1zaXplOiAxLjVyZW07XG4gIGNvbG9yOiAjMmMzZTUwO1xuICBtYXJnaW4tYm90dG9tOiAxNXB4O1xufVxuLnByb2plY3QtY29udGVudCBwIHtcbiAgY29sb3I6ICM2NjY7XG4gIGxpbmUtaGVpZ2h0OiAxLjY7XG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XG59XG5cbi5wcm9qZWN0LXRlY2gge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LXdyYXA6IHdyYXA7XG4gIGdhcDogMTBweDtcbn1cblxuLnRlY2gtdGFnIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcbiAgY29sb3I6IHdoaXRlO1xuICBwYWRkaW5nOiA1cHggMTJweDtcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgZm9udC1zaXplOiAwLjg1cmVtO1xuICBmb250LXdlaWdodDogNTAwO1xufVxuXG4uYWxsLXByb2plY3RzIHtcbiAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbn1cblxuLmNhdGVnb3J5LWZpbHRlciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBmbGV4LXdyYXA6IHdyYXA7XG4gIGdhcDogMTVweDtcbiAgbWFyZ2luLWJvdHRvbTogNTBweDtcbn1cblxuLmZpbHRlci1idG4ge1xuICBwYWRkaW5nOiAxMHB4IDI1cHg7XG4gIGJvcmRlcjogMnB4IHNvbGlkICM2NjdlZWE7XG4gIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICBjb2xvcjogIzY2N2VlYTtcbiAgYm9yZGVyLXJhZGl1czogMjVweDtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuLmZpbHRlci1idG46aG92ZXIsIC5maWx0ZXItYnRuLmFjdGl2ZSB7XG4gIGJhY2tncm91bmQ6ICM2NjdlZWE7XG4gIGNvbG9yOiB3aGl0ZTtcbn1cblxuLnByb2plY3RzLWdyaWQge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDM1MHB4LCAxZnIpKTtcbiAgZ2FwOiAzMHB4O1xufVxuXG4ucHJvamVjdC1jYXJkIHtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIGJveC1zaGFkb3c6IDAgNXB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjA4KTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cbi5wcm9qZWN0LWNhcmQ6aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTVweCk7XG4gIGJveC1zaGFkb3c6IDAgMTVweCAzMHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG59XG5cbi5jYXJkLWltYWdlIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBoZWlnaHQ6IDIwMHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuLmNhcmQtaW1hZ2UgaW1nIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMTAwJTtcbiAgb2JqZWN0LWZpdDogY292ZXI7XG4gIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7XG59XG4uY2FyZC1pbWFnZTpob3ZlciBpbWcge1xuICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xufVxuXG4uY2FyZC1vdmVybGF5IHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICBib3R0b206IDA7XG4gIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC43KTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIG9wYWNpdHk6IDA7XG4gIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zcyBlYXNlO1xufVxuLmNhcmQtaW1hZ2U6aG92ZXIgLmNhcmQtb3ZlcmxheSB7XG4gIG9wYWNpdHk6IDE7XG59XG5cbi5jYXJkLWFjdGlvbnMge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDE1cHg7XG59XG5cbi5hY3Rpb24tYnRuIHtcbiAgd2lkdGg6IDUwcHg7XG4gIGhlaWdodDogNTBweDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBib3JkZXI6IDJweCBzb2xpZCB3aGl0ZTtcbiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGN1cnNvcjogcG9pbnRlcjtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cbi5hY3Rpb24tYnRuOmhvdmVyIHtcbiAgYmFja2dyb3VuZDogd2hpdGU7XG4gIGNvbG9yOiAjMzMzO1xufVxuXG4uY2FyZC1jb250ZW50IHtcbiAgcGFkZGluZzogMjVweDtcbn1cbi5jYXJkLWNvbnRlbnQgaDMge1xuICBmb250LXNpemU6IDEuM3JlbTtcbiAgY29sb3I6ICMyYzNlNTA7XG4gIG1hcmdpbi1ib3R0b206IDEwcHg7XG59XG4uY2FyZC1jb250ZW50IHAge1xuICBjb2xvcjogIzY2NjtcbiAgbGluZS1oZWlnaHQ6IDEuNjtcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcbiAgZm9udC1zaXplOiAwLjk1cmVtO1xufVxuXG4uY2FyZC10ZWNoIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC13cmFwOiB3cmFwO1xuICBnYXA6IDhweDtcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcbn1cblxuLnRlY2gtYmFkZ2Uge1xuICBiYWNrZ3JvdW5kOiAjZjhmOWZhO1xuICBjb2xvcjogIzY2N2VlYTtcbiAgcGFkZGluZzogNHB4IDEwcHg7XG4gIGJvcmRlci1yYWRpdXM6IDE1cHg7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBmb250LXdlaWdodDogNTAwO1xuICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmO1xufVxuXG4uY2FyZC1mb290ZXIgLmJ0bi1zbSB7XG4gIHBhZGRpbmc6IDhweCAyMHB4O1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cblxuLmVtcHR5LXN0YXRlIHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBwYWRkaW5nOiA2MHB4IDIwcHg7XG4gIGNvbG9yOiAjNjY2O1xufVxuLmVtcHR5LXN0YXRlIGkge1xuICBmb250LXNpemU6IDRyZW07XG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XG4gIG9wYWNpdHk6IDAuNTtcbn1cbi5lbXB0eS1zdGF0ZSBoMyB7XG4gIGZvbnQtc2l6ZTogMS41cmVtO1xuICBtYXJnaW4tYm90dG9tOiAxMHB4O1xuICBjb2xvcjogIzJjM2U1MDtcbn1cblxuLmN0YS1zZWN0aW9uIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcbiAgY29sb3I6IHdoaXRlO1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi5jdGEtY29udGVudCBoMiB7XG4gIGZvbnQtc2l6ZTogMi41cmVtO1xuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xufVxuLmN0YS1jb250ZW50IHAge1xuICBmb250LXNpemU6IDEuMnJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMzBweDtcbiAgb3BhY2l0eTogMC45O1xufVxuLmN0YS1jb250ZW50IC5idG4ge1xuICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgY29sb3I6ICM2NjdlZWE7XG59XG4uY3RhLWNvbnRlbnQgLmJ0bjpob3ZlciB7XG4gIGJhY2tncm91bmQ6ICNmOGY5ZmE7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcbn1cblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5mZWF0dXJlZC1ncmlkIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgfVxuICAucHJvamVjdHMtZ3JpZCB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gIH1cbiAgLmNhdGVnb3J5LWZpbHRlciB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICB9XG4gIC5wcm9qZWN0LWFjdGlvbnMsXG4gIC5jYXJkLWFjdGlvbnMge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiAxMHB4O1xuICB9XG4gIC5jdGEtY29udGVudCBoMiB7XG4gICAgZm9udC1zaXplOiAycmVtO1xuICB9XG59XG5AbWVkaWEgKG1heC13aWR0aDogNDgwcHgpIHtcbiAgLnByb2plY3QtY29udGVudCxcbiAgLmNhcmQtY29udGVudCB7XG4gICAgcGFkZGluZzogMjBweDtcbiAgfVxuICAuZmVhdHVyZWQtcHJvamVjdCxcbiAgLnByb2plY3QtY2FyZCB7XG4gICAgbWFyZ2luOiAwIDEwcHg7XG4gIH1cbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "tech_r4", "ɵɵelement", "ɵɵlistener", "ProjectsComponent_div_15_Template_button_click_5_listener", "project_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "openDemo", "demoUrl", "ProjectsComponent_div_15_Template_button_click_8_listener", "openGithub", "githubUrl", "ɵɵtemplate", "ProjectsComponent_div_15_span_17_Template", "ɵɵstyleProp", "i_r5", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "title", "longDescription", "technologies", "ProjectsComponent_button_24_Template_button_click_0_listener", "category_r7", "_r6", "selectCategory", "id", "ɵɵclassProp", "selectedCate<PERSON><PERSON>", "ɵɵtextInterpolate1", "name", "tech_r10", "ProjectsComponent_div_26_Template_button_click_5_listener", "project_r9", "_r8", "ProjectsComponent_div_26_Template_button_click_7_listener", "ProjectsComponent_div_26_span_15_Template", "ProjectsComponent_div_26_Template_button_click_17_listener", "i_r11", "description", "ProjectsComponent", "constructor", "categories", "projects", "featured", "category", "filteredProjects", "filter", "project", "featuredProjects", "categoryId", "url", "window", "open", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProjectsComponent_Template", "rf", "ctx", "ProjectsComponent_div_15_Template", "ProjectsComponent_button_24_Template", "ProjectsComponent_div_26_Template", "ProjectsComponent_div_27_Template", "length", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\pages\\projects\\projects.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\pages\\projects\\projects.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\ninterface Project {\n  id: number;\n  title: string;\n  description: string;\n  longDescription: string;\n  image: string;\n  technologies: string[];\n  demoUrl: string;\n  githubUrl: string;\n  featured: boolean;\n  category: string;\n}\n\n@Component({\n  selector: 'app-projects',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  templateUrl: './projects.component.html',\n  styleUrls: ['./projects.component.scss']\n})\nexport class ProjectsComponent {\n  selectedCategory = 'all';\n  \n  categories = [\n    { id: 'all', name: 'All Projects' },\n    { id: 'web', name: 'Web Apps' },\n    { id: 'angular', name: 'Angular' },\n    { id: 'responsive', name: 'Responsive' }\n  ];\n\n  projects: Project[] = [\n    {\n      id: 1,\n      title: 'E-Commerce Dashboard',\n      description: 'Modern admin dashboard for e-commerce management with real-time analytics.',\n      longDescription: 'A comprehensive admin dashboard built with Angular featuring real-time sales analytics, inventory management, and customer insights. Includes responsive design and interactive charts.',\n      image: 'https://via.placeholder.com/400x250/667eea/ffffff?text=E-Commerce+Dashboard',\n      technologies: ['Angular', 'TypeScript', 'SCSS', 'Chart.js'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: true,\n      category: 'angular'\n    },\n    {\n      id: 2,\n      title: 'Task Management App',\n      description: 'Collaborative task management application with drag-and-drop functionality.',\n      longDescription: 'A Kanban-style task management application that allows teams to organize projects, assign tasks, and track progress with an intuitive drag-and-drop interface.',\n      image: 'https://via.placeholder.com/400x250/764ba2/ffffff?text=Task+Manager',\n      technologies: ['Angular', 'RxJS', 'Angular CDK', 'SCSS'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: true,\n      category: 'web'\n    },\n    {\n      id: 3,\n      title: 'Weather App',\n      description: 'Beautiful weather application with location-based forecasts and animations.',\n      longDescription: 'A responsive weather application that provides current weather conditions and 7-day forecasts with smooth animations and beautiful UI design.',\n      image: 'https://via.placeholder.com/400x250/4ecdc4/ffffff?text=Weather+App',\n      technologies: ['JavaScript', 'CSS3', 'Weather API', 'HTML5'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: false,\n      category: 'web'\n    },\n    {\n      id: 4,\n      title: 'Portfolio Website',\n      description: 'Personal portfolio website showcasing projects and skills.',\n      longDescription: 'This very portfolio website built with Angular, featuring responsive design, smooth animations, and modern UI components.',\n      image: 'https://via.placeholder.com/400x250/667eea/ffffff?text=Portfolio+Site',\n      technologies: ['Angular', 'TypeScript', 'SCSS', 'Responsive Design'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: true,\n      category: 'responsive'\n    },\n    {\n      id: 5,\n      title: 'Restaurant Landing Page',\n      description: 'Elegant landing page for a restaurant with online reservation system.',\n      longDescription: 'A beautiful, responsive landing page for a restaurant featuring menu showcase, online reservations, and contact information with smooth scrolling effects.',\n      image: 'https://via.placeholder.com/400x250/ff6b6b/ffffff?text=Restaurant+Site',\n      technologies: ['HTML5', 'CSS3', 'JavaScript', 'Responsive Design'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: false,\n      category: 'responsive'\n    },\n    {\n      id: 6,\n      title: 'Blog Platform',\n      description: 'Modern blog platform with content management and user authentication.',\n      longDescription: 'A full-featured blog platform with user authentication, content management, commenting system, and responsive design.',\n      image: 'https://via.placeholder.com/400x250/45b7d1/ffffff?text=Blog+Platform',\n      technologies: ['Angular', 'TypeScript', 'SCSS', 'Firebase'],\n      demoUrl: '#',\n      githubUrl: '#',\n      featured: false,\n      category: 'angular'\n    }\n  ];\n\n  get filteredProjects() {\n    if (this.selectedCategory === 'all') {\n      return this.projects;\n    }\n    return this.projects.filter(project => project.category === this.selectedCategory);\n  }\n\n  get featuredProjects() {\n    return this.projects.filter(project => project.featured);\n  }\n\n  selectCategory(categoryId: string) {\n    this.selectedCategory = categoryId;\n  }\n\n  openDemo(url: string) {\n    if (url !== '#') {\n      window.open(url, '_blank');\n    }\n  }\n\n  openGithub(url: string) {\n    if (url !== '#') {\n      window.open(url, '_blank');\n    }\n  }\n}\n", "<section class=\"projects-hero section\">\n  <div class=\"container\">\n    <div class=\"section-title\">\n      <h1>My Projects</h1>\n      <p>A showcase of my work and the technologies I've mastered</p>\n    </div>\n  </div>\n</section>\n\n<section class=\"featured-projects section\">\n  <div class=\"container\">\n    <div class=\"section-title\">\n      <h2>Featured Projects</h2>\n      <p>Highlighting my best work and most challenging projects</p>\n    </div>\n    \n    <div class=\"featured-grid\">\n      <div class=\"featured-project fade-in-up\" *ngFor=\"let project of featuredProjects; let i = index\" [style.animation-delay]=\"i * 0.2 + 's'\">\n        <div class=\"project-image\">\n          <img [src]=\"project.image\" [alt]=\"project.title\">\n          <div class=\"project-overlay\">\n            <div class=\"project-actions\">\n              <button class=\"btn btn-primary\" (click)=\"openDemo(project.demoUrl)\">\n                <i class=\"fas fa-external-link-alt\"></i> Live Demo\n              </button>\n              <button class=\"btn btn-outline\" (click)=\"openGithub(project.githubUrl)\">\n                <i class=\"fab fa-github\"></i> GitHub\n              </button>\n            </div>\n          </div>\n        </div>\n        <div class=\"project-content\">\n          <h3>{{ project.title }}</h3>\n          <p>{{ project.longDescription }}</p>\n          <div class=\"project-tech\">\n            <span class=\"tech-tag\" *ngFor=\"let tech of project.technologies\">{{ tech }}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</section>\n\n<section class=\"all-projects section\">\n  <div class=\"container\">\n    <div class=\"section-title\">\n      <h2>All Projects</h2>\n      <p>Browse through all my projects by category</p>\n    </div>\n    \n    <!-- Category Filter -->\n    <div class=\"category-filter\">\n      <button \n        *ngFor=\"let category of categories\"\n        class=\"filter-btn\"\n        [class.active]=\"selectedCategory === category.id\"\n        (click)=\"selectCategory(category.id)\">\n        {{ category.name }}\n      </button>\n    </div>\n    \n    <!-- Projects Grid -->\n    <div class=\"projects-grid\">\n      <div class=\"project-card fade-in-up\" *ngFor=\"let project of filteredProjects; let i = index\" [style.animation-delay]=\"i * 0.1 + 's'\">\n        <div class=\"card-image\">\n          <img [src]=\"project.image\" [alt]=\"project.title\">\n          <div class=\"card-overlay\">\n            <div class=\"card-actions\">\n              <button class=\"action-btn\" (click)=\"openDemo(project.demoUrl)\" title=\"Live Demo\">\n                <i class=\"fas fa-external-link-alt\"></i>\n              </button>\n              <button class=\"action-btn\" (click)=\"openGithub(project.githubUrl)\" title=\"GitHub\">\n                <i class=\"fab fa-github\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"card-content\">\n          <h3>{{ project.title }}</h3>\n          <p>{{ project.description }}</p>\n          \n          <div class=\"card-tech\">\n            <span class=\"tech-badge\" *ngFor=\"let tech of project.technologies\">{{ tech }}</span>\n          </div>\n          \n          <div class=\"card-footer\">\n            <button class=\"btn btn-primary btn-sm\" (click)=\"openDemo(project.demoUrl)\">\n              View Project\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- Empty State -->\n    <div class=\"empty-state\" *ngIf=\"filteredProjects.length === 0\">\n      <i class=\"fas fa-folder-open\"></i>\n      <h3>No projects found</h3>\n      <p>No projects match the selected category.</p>\n    </div>\n  </div>\n</section>\n\n<section class=\"cta-section section\">\n  <div class=\"container\">\n    <div class=\"cta-content\">\n      <h2>Interested in Working Together?</h2>\n      <p>I'm always open to discussing new opportunities and exciting projects.</p>\n      <a routerLink=\"/contact\" class=\"btn btn-primary\">Get In Touch</a>\n    </div>\n  </div>\n</section>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;;;ICiClCC,EAAA,CAAAC,cAAA,eAAiE;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAjBH,EAAA,CAAAI,SAAA,EAAU;IAAVJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAU;;;;;;IAjB/EN,EADF,CAAAC,cAAA,cAAyI,cAC5G;IACzBD,EAAA,CAAAO,SAAA,cAAiD;IAG7CP,EAFJ,CAAAC,cAAA,cAA6B,cACE,iBACyC;IAApCD,EAAA,CAAAQ,UAAA,mBAAAC,0DAAA;MAAA,MAAAC,UAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAAP,UAAA,CAAAQ,OAAA,CAAyB;IAAA,EAAC;IACjElB,EAAA,CAAAO,SAAA,YAAwC;IAACP,EAAA,CAAAE,MAAA,kBAC3C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAwE;IAAxCD,EAAA,CAAAQ,UAAA,mBAAAW,0DAAA;MAAA,MAAAT,UAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAM,UAAA,CAAAV,UAAA,CAAAW,SAAA,CAA6B;IAAA,EAAC;IACrErB,EAAA,CAAAO,SAAA,YAA6B;IAACP,EAAA,CAAAE,MAAA,gBAChC;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAEJH,EADF,CAAAC,cAAA,eAA6B,UACvB;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACpCH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAsB,UAAA,KAAAC,yCAAA,mBAAiE;IAGvEvB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;;IArB2FH,EAAA,CAAAwB,WAAA,oBAAAC,IAAA,aAAuC;IAE/HzB,EAAA,CAAAI,SAAA,GAAqB;IAACJ,EAAtB,CAAA0B,UAAA,QAAAhB,UAAA,CAAAiB,KAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAAqB,QAAAlB,UAAA,CAAAmB,KAAA,CAAsB;IAa5C7B,EAAA,CAAAI,SAAA,IAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAmB,KAAA,CAAmB;IACpB7B,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAoB,eAAA,CAA6B;IAEU9B,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA0B,UAAA,YAAAhB,UAAA,CAAAqB,YAAA,CAAuB;;;;;;IAiBrE/B,EAAA,CAAAC,cAAA,iBAIwC;IAAtCD,EAAA,CAAAQ,UAAA,mBAAAwB,6DAAA;MAAA,MAAAC,WAAA,GAAAjC,EAAA,CAAAW,aAAA,CAAAuB,GAAA,EAAArB,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAqB,cAAA,CAAAF,WAAA,CAAAG,EAAA,CAA2B;IAAA,EAAC;IACrCpC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAHPH,EAAA,CAAAqC,WAAA,WAAAvB,MAAA,CAAAwB,gBAAA,KAAAL,WAAA,CAAAG,EAAA,CAAiD;IAEjDpC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAuC,kBAAA,MAAAN,WAAA,CAAAO,IAAA,MACF;;;;;IAyBMxC,EAAA,CAAAC,cAAA,eAAmE;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAjBH,EAAA,CAAAI,SAAA,EAAU;IAAVJ,EAAA,CAAAK,iBAAA,CAAAoC,QAAA,CAAU;;;;;;IAnBjFzC,EADF,CAAAC,cAAA,cAAqI,cAC3G;IACtBD,EAAA,CAAAO,SAAA,cAAiD;IAG7CP,EAFJ,CAAAC,cAAA,cAA0B,cACE,iBACyD;IAAtDD,EAAA,CAAAQ,UAAA,mBAAAkC,0DAAA;MAAA,MAAAC,UAAA,GAAA3C,EAAA,CAAAW,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAA0B,UAAA,CAAAzB,OAAA,CAAyB;IAAA,EAAC;IAC5DlB,EAAA,CAAAO,SAAA,YAAwC;IAC1CP,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAkF;IAAvDD,EAAA,CAAAQ,UAAA,mBAAAqC,0DAAA;MAAA,MAAAF,UAAA,GAAA3C,EAAA,CAAAW,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAM,UAAA,CAAAuB,UAAA,CAAAtB,SAAA,CAA6B;IAAA,EAAC;IAChErB,EAAA,CAAAO,SAAA,YAA6B;IAIrCP,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAGJH,EADF,CAAAC,cAAA,cAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhCH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAsB,UAAA,KAAAwB,yCAAA,mBAAmE;IACrE9C,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAAyB,kBACoD;IAApCD,EAAA,CAAAQ,UAAA,mBAAAuC,2DAAA;MAAA,MAAAJ,UAAA,GAAA3C,EAAA,CAAAW,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAA0B,UAAA,CAAAzB,OAAA,CAAyB;IAAA,EAAC;IACxElB,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;;IA7BuFH,EAAA,CAAAwB,WAAA,oBAAAwB,KAAA,aAAuC;IAE3HhD,EAAA,CAAAI,SAAA,GAAqB;IAACJ,EAAtB,CAAA0B,UAAA,QAAAiB,UAAA,CAAAhB,KAAA,EAAA3B,EAAA,CAAA4B,aAAA,CAAqB,QAAAe,UAAA,CAAAd,KAAA,CAAsB;IAc5C7B,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAsC,UAAA,CAAAd,KAAA,CAAmB;IACpB7B,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAsC,UAAA,CAAAM,WAAA,CAAyB;IAGgBjD,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA0B,UAAA,YAAAiB,UAAA,CAAAZ,YAAA,CAAuB;;;;;IAazE/B,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAO,SAAA,YAAkC;IAClCP,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAC7CF,EAD6C,CAAAG,YAAA,EAAI,EAC3C;;;AD5EV,OAAM,MAAO+C,iBAAiB;EAP9BC,YAAA;IAQE,KAAAb,gBAAgB,GAAG,KAAK;IAExB,KAAAc,UAAU,GAAG,CACX;MAAEhB,EAAE,EAAE,KAAK;MAAEI,IAAI,EAAE;IAAc,CAAE,EACnC;MAAEJ,EAAE,EAAE,KAAK;MAAEI,IAAI,EAAE;IAAU,CAAE,EAC/B;MAAEJ,EAAE,EAAE,SAAS;MAAEI,IAAI,EAAE;IAAS,CAAE,EAClC;MAAEJ,EAAE,EAAE,YAAY;MAAEI,IAAI,EAAE;IAAY,CAAE,CACzC;IAED,KAAAa,QAAQ,GAAc,CACpB;MACEjB,EAAE,EAAE,CAAC;MACLP,KAAK,EAAE,sBAAsB;MAC7BoB,WAAW,EAAE,4EAA4E;MACzFnB,eAAe,EAAE,yLAAyL;MAC1MH,KAAK,EAAE,6EAA6E;MACpFI,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU,CAAC;MAC3Db,OAAO,EAAE,GAAG;MACZG,SAAS,EAAE,GAAG;MACdiC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,EACD;MACEnB,EAAE,EAAE,CAAC;MACLP,KAAK,EAAE,qBAAqB;MAC5BoB,WAAW,EAAE,6EAA6E;MAC1FnB,eAAe,EAAE,gKAAgK;MACjLH,KAAK,EAAE,qEAAqE;MAC5EI,YAAY,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,CAAC;MACxDb,OAAO,EAAE,GAAG;MACZG,SAAS,EAAE,GAAG;MACdiC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,EACD;MACEnB,EAAE,EAAE,CAAC;MACLP,KAAK,EAAE,aAAa;MACpBoB,WAAW,EAAE,6EAA6E;MAC1FnB,eAAe,EAAE,+IAA+I;MAChKH,KAAK,EAAE,oEAAoE;MAC3EI,YAAY,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC;MAC5Db,OAAO,EAAE,GAAG;MACZG,SAAS,EAAE,GAAG;MACdiC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE;KACX,EACD;MACEnB,EAAE,EAAE,CAAC;MACLP,KAAK,EAAE,mBAAmB;MAC1BoB,WAAW,EAAE,4DAA4D;MACzEnB,eAAe,EAAE,2HAA2H;MAC5IH,KAAK,EAAE,uEAAuE;MAC9EI,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,mBAAmB,CAAC;MACpEb,OAAO,EAAE,GAAG;MACZG,SAAS,EAAE,GAAG;MACdiC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;KACX,EACD;MACEnB,EAAE,EAAE,CAAC;MACLP,KAAK,EAAE,yBAAyB;MAChCoB,WAAW,EAAE,uEAAuE;MACpFnB,eAAe,EAAE,4JAA4J;MAC7KH,KAAK,EAAE,wEAAwE;MAC/EI,YAAY,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,mBAAmB,CAAC;MAClEb,OAAO,EAAE,GAAG;MACZG,SAAS,EAAE,GAAG;MACdiC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE;KACX,EACD;MACEnB,EAAE,EAAE,CAAC;MACLP,KAAK,EAAE,eAAe;MACtBoB,WAAW,EAAE,uEAAuE;MACpFnB,eAAe,EAAE,uHAAuH;MACxIH,KAAK,EAAE,sEAAsE;MAC7EI,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU,CAAC;MAC3Db,OAAO,EAAE,GAAG;MACZG,SAAS,EAAE,GAAG;MACdiC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE;KACX,CACF;;EAED,IAAIC,gBAAgBA,CAAA;IAClB,IAAI,IAAI,CAAClB,gBAAgB,KAAK,KAAK,EAAE;MACnC,OAAO,IAAI,CAACe,QAAQ;;IAEtB,OAAO,IAAI,CAACA,QAAQ,CAACI,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACH,QAAQ,KAAK,IAAI,CAACjB,gBAAgB,CAAC;EACpF;EAEA,IAAIqB,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACN,QAAQ,CAACI,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACJ,QAAQ,CAAC;EAC1D;EAEAnB,cAAcA,CAACyB,UAAkB;IAC/B,IAAI,CAACtB,gBAAgB,GAAGsB,UAAU;EACpC;EAEA3C,QAAQA,CAAC4C,GAAW;IAClB,IAAIA,GAAG,KAAK,GAAG,EAAE;MACfC,MAAM,CAACC,IAAI,CAACF,GAAG,EAAE,QAAQ,CAAC;;EAE9B;EAEAzC,UAAUA,CAACyC,GAAW;IACpB,IAAIA,GAAG,KAAK,GAAG,EAAE;MACfC,MAAM,CAACC,IAAI,CAACF,GAAG,EAAE,QAAQ,CAAC;;EAE9B;;;uBA9GWX,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAc,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAlE,EAAA,CAAAmE,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBxBzE,EAHN,CAAAC,cAAA,iBAAuC,aACd,aACM,SACrB;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,+DAAwD;UAGjEF,EAHiE,CAAAG,YAAA,EAAI,EAC3D,EACF,EACE;UAKJH,EAHN,CAAAC,cAAA,iBAA2C,aAClB,aACM,UACrB;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,+DAAuD;UAC5DF,EAD4D,CAAAG,YAAA,EAAI,EAC1D;UAENH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAsB,UAAA,KAAAqD,iCAAA,kBAAyI;UAwB/I3E,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKJH,EAHN,CAAAC,cAAA,kBAAsC,cACb,cACM,UACrB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,kDAA0C;UAC/CF,EAD+C,CAAAG,YAAA,EAAI,EAC7C;UAGNH,EAAA,CAAAC,cAAA,cAA6B;UAC3BD,EAAA,CAAAsB,UAAA,KAAAsD,oCAAA,oBAIwC;UAG1C5E,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAA2B;UACzBD,EAAA,CAAAsB,UAAA,KAAAuD,iCAAA,mBAAqI;UA8BvI7E,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAsB,UAAA,KAAAwD,iCAAA,kBAA+D;UAMnE9E,EADE,CAAAG,YAAA,EAAM,EACE;UAKJH,EAHN,CAAAC,cAAA,mBAAqC,cACZ,eACI,UACnB;UAAAD,EAAA,CAAAE,MAAA,uCAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,8EAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7EH,EAAA,CAAAC,cAAA,aAAiD;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAGnEF,EAHmE,CAAAG,YAAA,EAAI,EAC7D,EACF,EACE;;;UA/FyDH,EAAA,CAAAI,SAAA,IAAqB;UAArBJ,EAAA,CAAA0B,UAAA,YAAAgD,GAAA,CAAAf,gBAAA,CAAqB;UAoC3D3D,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAA0B,UAAA,YAAAgD,GAAA,CAAAtB,UAAA,CAAa;UAUqBpD,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAA0B,UAAA,YAAAgD,GAAA,CAAAlB,gBAAA,CAAqB;UAiCtDxD,EAAA,CAAAI,SAAA,EAAmC;UAAnCJ,EAAA,CAAA0B,UAAA,SAAAgD,GAAA,CAAAlB,gBAAA,CAAAuB,MAAA,OAAmC;;;qBD5ErDjF,YAAY,EAAAkF,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEnF,YAAY,EAAAoF,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}