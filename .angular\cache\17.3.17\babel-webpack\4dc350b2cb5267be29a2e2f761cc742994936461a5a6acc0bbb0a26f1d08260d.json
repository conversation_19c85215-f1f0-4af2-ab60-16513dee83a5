{"ast": null, "code": "export const routes = [{\n  path: '',\n  loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent)\n}, {\n  path: 'about',\n  loadComponent: () => import('./pages/about/about.component').then(m => m.AboutComponent)\n}, {\n  path: 'skills',\n  loadComponent: () => import('./pages/skills/skills.component').then(m => m.SkillsComponent)\n}, {\n  path: 'projects',\n  loadComponent: () => import('./pages/projects/projects.component').then(m => m.ProjectsComponent)\n}, {\n  path: 'contact',\n  loadComponent: () => import('./pages/contact/contact.component').then(m => m.ContactComponent)\n}, {\n  path: '**',\n  redirectTo: ''\n}];", "map": {"version": 3, "names": ["routes", "path", "loadComponent", "then", "m", "HomeComponent", "AboutComponent", "SkillsComponent", "ProjectsComponent", "ContactComponent", "redirectTo"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent)\n  },\n  {\n    path: 'about',\n    loadComponent: () => import('./pages/about/about.component').then(m => m.AboutComponent)\n  },\n  {\n    path: 'skills',\n    loadComponent: () => import('./pages/skills/skills.component').then(m => m.SkillsComponent)\n  },\n  {\n    path: 'projects',\n    loadComponent: () => import('./pages/projects/projects.component').then(m => m.ProjectsComponent)\n  },\n  {\n    path: 'contact',\n    loadComponent: () => import('./pages/contact/contact.component').then(m => m.ContactComponent)\n  },\n  {\n    path: '**',\n    redirectTo: ''\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa;CACrF,EACD;EACEJ,IAAI,EAAE,OAAO;EACbC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,cAAc;CACxF,EACD;EACEL,IAAI,EAAE,QAAQ;EACdC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,eAAe;CAC3F,EACD;EACEN,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,iBAAiB;CACjG,EACD;EACEP,IAAI,EAAE,SAAS;EACfC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,gBAAgB;CAC9F,EACD;EACER,IAAI,EAAE,IAAI;EACVS,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}