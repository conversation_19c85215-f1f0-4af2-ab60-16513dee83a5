{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class HomeComponent {\n  constructor() {\n    this.typedText = '';\n    this.fullText = 'Frontend Web Developer | Angular Developer';\n    this.typingSpeed = 100;\n    this.deletingSpeed = 50;\n    this.pauseTime = 2000;\n    this.isDeleting = false;\n    this.charIndex = 0;\n  }\n  ngOnInit() {\n    this.startTyping();\n  }\n  ngOnDestroy() {\n    if (this.typingInterval) {\n      clearTimeout(this.typingInterval);\n    }\n  }\n  startTyping() {\n    const type = () => {\n      if (!this.isDeleting && this.charIndex < this.fullText.length) {\n        this.typedText += this.fullText.charAt(this.charIndex);\n        this.charIndex++;\n        this.typingInterval = setTimeout(type, this.typingSpeed);\n      } else if (this.isDeleting && this.charIndex > 0) {\n        this.typedText = this.fullText.substring(0, this.charIndex - 1);\n        this.charIndex--;\n        this.typingInterval = setTimeout(type, this.deletingSpeed);\n      } else {\n        this.isDeleting = !this.isDeleting;\n        this.typingInterval = setTimeout(type, this.pauseTime);\n      }\n    };\n    type();\n  }\n  scrollToAbout() {\n    // This will be handled by router navigation\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 24,\n      vars: 1,\n      consts: [[1, \"hero\"], [1, \"hero-background\"], [1, \"container\"], [1, \"hero-content\", \"fade-in-up\"], [1, \"hero-text\"], [1, \"hero-title\"], [1, \"highlight\"], [1, \"typing-container\"], [1, \"typed-text\"], [1, \"cursor\"], [1, \"hero-description\"], [1, \"hero-buttons\"], [\"routerLink\", \"/about\", 1, \"btn\", \"btn-primary\"], [\"routerLink\", \"/projects\", 1, \"btn\", \"btn-outline\"], [1, \"scroll-indicator\"], [1, \"scroll-arrow\"], [1, \"fas\", \"fa-chevron-down\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h1\", 5);\n          i0.ɵɵtext(6, \" Hi, I'm \");\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"Abdullah G\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"span\", 8);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"span\", 9);\n          i0.ɵɵtext(13, \"|\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"p\", 10);\n          i0.ɵɵtext(15, \" Passionate about creating beautiful, responsive web applications with modern technologies. Welcome to my digital portfolio where creativity meets functionality. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 11)(17, \"a\", 12);\n          i0.ɵɵtext(18, \"Learn More About Me\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"a\", 13);\n          i0.ɵɵtext(20, \"View My Work\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\", 15);\n          i0.ɵɵelement(23, \"i\", 16);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.typedText);\n        }\n      },\n      dependencies: [CommonModule, RouterModule, i1.RouterLink],\n      styles: [\".hero[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n  overflow: hidden;\\n  padding-top: 80px;\\n}\\n\\n.hero-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  opacity: 0.1;\\n  z-index: -1;\\n}\\n.hero-background[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"1\\\" fill=\\\"%23667eea\\\" opacity=\\\"0.1\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');\\n}\\n\\n.hero-content[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: center;\\n  z-index: 1;\\n}\\n\\n.hero-title[_ngcontent-%COMP%] {\\n  font-size: 3.5rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 20px;\\n  line-height: 1.2;\\n}\\n.hero-title[_ngcontent-%COMP%]   .highlight[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.typing-container[_ngcontent-%COMP%] {\\n  height: 60px;\\n  margin-bottom: 30px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.typed-text[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 500;\\n  color: #667eea;\\n}\\n\\n.cursor[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #667eea;\\n  animation: _ngcontent-%COMP%_blink 1s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_blink {\\n  0%, 50% {\\n    opacity: 1;\\n  }\\n  51%, 100% {\\n    opacity: 0;\\n  }\\n}\\n.hero-description[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #666;\\n  max-width: 600px;\\n  margin: 0 auto 40px;\\n  line-height: 1.6;\\n}\\n\\n.hero-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  justify-content: center;\\n  flex-wrap: wrap;\\n}\\n\\n.scroll-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 30px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  animation: _ngcontent-%COMP%_bounce 2s infinite;\\n}\\n\\n.scroll-arrow[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: rgba(102, 126, 234, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #667eea;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.scroll-arrow[_ngcontent-%COMP%]:hover {\\n  background: rgba(102, 126, 234, 0.2);\\n  transform: translateY(-2px);\\n}\\n\\n@keyframes _ngcontent-%COMP%_bounce {\\n  0%, 20%, 50%, 80%, 100% {\\n    transform: translateX(-50%) translateY(0);\\n  }\\n  40% {\\n    transform: translateX(-50%) translateY(-10px);\\n  }\\n  60% {\\n    transform: translateX(-50%) translateY(-5px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  .typed-text[_ngcontent-%COMP%], .cursor[_ngcontent-%COMP%] {\\n    font-size: 1.2rem;\\n  }\\n  .hero-description[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    padding: 0 20px;\\n  }\\n  .hero-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n    gap: 15px;\\n  }\\n  .hero-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 200px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .typed-text[_ngcontent-%COMP%], .cursor[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "HomeComponent", "constructor", "typedText", "fullText", "typingSpeed", "deletingSpeed", "pauseTime", "isDeleting", "charIndex", "ngOnInit", "startTyping", "ngOnDestroy", "typingInterval", "clearTimeout", "type", "length", "char<PERSON>t", "setTimeout", "substring", "scrollToAbout", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "i1", "RouterLink", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\pages\\home\\home.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\pages\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.scss']\n})\nexport class HomeComponent implements OnInit, OnDestroy {\n  typedText = '';\n  fullText = 'Frontend Web Developer | Angular Developer';\n  typingSpeed = 100;\n  deletingSpeed = 50;\n  pauseTime = 2000;\n  isDeleting = false;\n  charIndex = 0;\n  typingInterval: any;\n\n  ngOnInit() {\n    this.startTyping();\n  }\n\n  ngOnDestroy() {\n    if (this.typingInterval) {\n      clearTimeout(this.typingInterval);\n    }\n  }\n\n  startTyping() {\n    const type = () => {\n      if (!this.isDeleting && this.charIndex < this.fullText.length) {\n        this.typedText += this.fullText.charAt(this.charIndex);\n        this.charIndex++;\n        this.typingInterval = setTimeout(type, this.typingSpeed);\n      } else if (this.isDeleting && this.charIndex > 0) {\n        this.typedText = this.fullText.substring(0, this.charIndex - 1);\n        this.charIndex--;\n        this.typingInterval = setTimeout(type, this.deletingSpeed);\n      } else {\n        this.isDeleting = !this.isDeleting;\n        this.typingInterval = setTimeout(type, this.pauseTime);\n      }\n    };\n    type();\n  }\n\n  scrollToAbout() {\n    // This will be handled by router navigation\n  }\n}\n", "<section class=\"hero\">\n  <div class=\"hero-background\"></div>\n  <div class=\"container\">\n    <div class=\"hero-content fade-in-up\">\n      <div class=\"hero-text\">\n        <h1 class=\"hero-title\">\n          Hi, I'm <span class=\"highlight\"><PERSON> G</span>\n        </h1>\n        <div class=\"typing-container\">\n          <span class=\"typed-text\">{{ typedText }}</span>\n          <span class=\"cursor\">|</span>\n        </div>\n        <p class=\"hero-description\">\n          Passionate about creating beautiful, responsive web applications with modern technologies.\n          Welcome to my digital portfolio where creativity meets functionality.\n        </p>\n        <div class=\"hero-buttons\">\n          <a routerLink=\"/about\" class=\"btn btn-primary\">Learn More About Me</a>\n          <a routerLink=\"/projects\" class=\"btn btn-outline\">View My Work</a>\n        </div>\n      </div>\n    </div>\n  </div>\n  \n  <!-- Scroll indicator -->\n  <div class=\"scroll-indicator\">\n    <div class=\"scroll-arrow\">\n      <i class=\"fas fa-chevron-down\"></i>\n    </div>\n  </div>\n</section>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;AAS9C,OAAM,MAAOC,aAAa;EAP1BC,YAAA;IAQE,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,QAAQ,GAAG,4CAA4C;IACvD,KAAAC,WAAW,GAAG,GAAG;IACjB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,SAAS,GAAG,IAAI;IAChB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,CAAC;;EAGbC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,cAAc,EAAE;MACvBC,YAAY,CAAC,IAAI,CAACD,cAAc,CAAC;;EAErC;EAEAF,WAAWA,CAAA;IACT,MAAMI,IAAI,GAAGA,CAAA,KAAK;MAChB,IAAI,CAAC,IAAI,CAACP,UAAU,IAAI,IAAI,CAACC,SAAS,GAAG,IAAI,CAACL,QAAQ,CAACY,MAAM,EAAE;QAC7D,IAAI,CAACb,SAAS,IAAI,IAAI,CAACC,QAAQ,CAACa,MAAM,CAAC,IAAI,CAACR,SAAS,CAAC;QACtD,IAAI,CAACA,SAAS,EAAE;QAChB,IAAI,CAACI,cAAc,GAAGK,UAAU,CAACH,IAAI,EAAE,IAAI,CAACV,WAAW,CAAC;OACzD,MAAM,IAAI,IAAI,CAACG,UAAU,IAAI,IAAI,CAACC,SAAS,GAAG,CAAC,EAAE;QAChD,IAAI,CAACN,SAAS,GAAG,IAAI,CAACC,QAAQ,CAACe,SAAS,CAAC,CAAC,EAAE,IAAI,CAACV,SAAS,GAAG,CAAC,CAAC;QAC/D,IAAI,CAACA,SAAS,EAAE;QAChB,IAAI,CAACI,cAAc,GAAGK,UAAU,CAACH,IAAI,EAAE,IAAI,CAACT,aAAa,CAAC;OAC3D,MAAM;QACL,IAAI,CAACE,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;QAClC,IAAI,CAACK,cAAc,GAAGK,UAAU,CAACH,IAAI,EAAE,IAAI,CAACR,SAAS,CAAC;;IAE1D,CAAC;IACDQ,IAAI,EAAE;EACR;EAEAK,aAAaA,CAAA;IACX;EAAA;;;uBAvCSnB,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAoB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX1BP,EAAA,CAAAS,cAAA,iBAAsB;UACpBT,EAAA,CAAAU,SAAA,aAAmC;UAI7BV,EAHN,CAAAS,cAAA,aAAuB,aACgB,aACZ,YACE;UACrBT,EAAA,CAAAW,MAAA,gBAAQ;UAAAX,EAAA,CAAAS,cAAA,cAAwB;UAAAT,EAAA,CAAAW,MAAA,iBAAU;UAC5CX,EAD4C,CAAAY,YAAA,EAAO,EAC9C;UAEHZ,EADF,CAAAS,cAAA,aAA8B,eACH;UAAAT,EAAA,CAAAW,MAAA,IAAe;UAAAX,EAAA,CAAAY,YAAA,EAAO;UAC/CZ,EAAA,CAAAS,cAAA,eAAqB;UAAAT,EAAA,CAAAW,MAAA,SAAC;UACxBX,EADwB,CAAAY,YAAA,EAAO,EACzB;UACNZ,EAAA,CAAAS,cAAA,aAA4B;UAC1BT,EAAA,CAAAW,MAAA,0KAEF;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAEFZ,EADF,CAAAS,cAAA,eAA0B,aACuB;UAAAT,EAAA,CAAAW,MAAA,2BAAmB;UAAAX,EAAA,CAAAY,YAAA,EAAI;UACtEZ,EAAA,CAAAS,cAAA,aAAkD;UAAAT,EAAA,CAAAW,MAAA,oBAAY;UAItEX,EAJsE,CAAAY,YAAA,EAAI,EAC9D,EACF,EACF,EACF;UAIJZ,EADF,CAAAS,cAAA,eAA8B,eACF;UACxBT,EAAA,CAAAU,SAAA,aAAmC;UAGzCV,EAFI,CAAAY,YAAA,EAAM,EACF,EACE;;;UArByBZ,EAAA,CAAAa,SAAA,IAAe;UAAfb,EAAA,CAAAc,iBAAA,CAAAN,GAAA,CAAA7B,SAAA,CAAe;;;qBDFtCJ,YAAY,EAAEC,YAAY,EAAAuC,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}