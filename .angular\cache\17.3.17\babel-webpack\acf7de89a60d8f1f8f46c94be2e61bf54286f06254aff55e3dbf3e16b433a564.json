{"ast": null, "code": "export const isArrayLike = x => x && typeof x.length === 'number' && typeof x !== 'function';", "map": {"version": 3, "names": ["isArrayLike", "x", "length"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/my folio/node_modules/rxjs/dist/esm/internal/util/isArrayLike.js"], "sourcesContent": ["export const isArrayLike = ((x) => x && typeof x.length === 'number' && typeof x !== 'function');\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAKC,CAAC,IAAKA,CAAC,IAAI,OAAOA,CAAC,CAACC,MAAM,KAAK,QAAQ,IAAI,OAAOD,CAAC,KAAK,UAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}