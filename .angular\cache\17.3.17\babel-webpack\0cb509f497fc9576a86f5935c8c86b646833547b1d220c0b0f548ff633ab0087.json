{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nimport { timer } from '../observable/timer';\nimport { innerFrom } from '../observable/innerFrom';\nexport function retry(configOrCount = Infinity) {\n  let config;\n  if (configOrCount && typeof configOrCount === 'object') {\n    config = configOrCount;\n  } else {\n    config = {\n      count: configOrCount\n    };\n  }\n  const {\n    count = Infinity,\n    delay,\n    resetOnSuccess = false\n  } = config;\n  return count <= 0 ? identity : operate((source, subscriber) => {\n    let soFar = 0;\n    let innerSub;\n    const subscribeForRetry = () => {\n      let syncUnsub = false;\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, value => {\n        if (resetOnSuccess) {\n          soFar = 0;\n        }\n        subscriber.next(value);\n      }, undefined, err => {\n        if (soFar++ < count) {\n          const resub = () => {\n            if (innerSub) {\n              innerSub.unsubscribe();\n              innerSub = null;\n              subscribeForRetry();\n            } else {\n              syncUnsub = true;\n            }\n          };\n          if (delay != null) {\n            const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(err, soFar));\n            const notifierSubscriber = createOperatorSubscriber(subscriber, () => {\n              notifierSubscriber.unsubscribe();\n              resub();\n            }, () => {\n              subscriber.complete();\n            });\n            notifier.subscribe(notifierSubscriber);\n          } else {\n            resub();\n          }\n        } else {\n          subscriber.error(err);\n        }\n      }));\n      if (syncUnsub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        subscribeForRetry();\n      }\n    };\n    subscribeForRetry();\n  });\n}\n//# sourceMappingURL=retry.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}