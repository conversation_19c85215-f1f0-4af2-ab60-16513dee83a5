{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction SkillsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 21)(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 22);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 23)(10, \"p\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 24)(13, \"div\", 25);\n    i0.ɵɵelement(14, \"div\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(16, \"svg\", 28);\n    i0.ɵɵelement(17, \"circle\", 29)(18, \"circle\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(19, \"div\", 31);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const skill_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"animation-delay\", i_r2 * 0.1 + \"s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", skill_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(skill_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(skill_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", skill_r1.level, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(skill_r1.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", skill_r1.color);\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"stroke-dasharray\", ctx_r2.getCircularProgress(skill_r1.level));\n    i0.ɵɵattribute(\"stroke\", skill_r1.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", skill_r1.level, \"%\");\n  }\n}\nfunction SkillsComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tool_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r5 * 0.1 + \"s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(tool_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tool_r4.name);\n  }\n}\nexport class SkillsComponent {\n  constructor() {\n    this.skills = [{\n      name: 'HTML5',\n      level: 95,\n      icon: 'fab fa-html5',\n      color: '#e34f26',\n      description: 'Semantic markup, accessibility, and modern HTML5 features'\n    }, {\n      name: 'CSS3',\n      level: 90,\n      icon: 'fab fa-css3-alt',\n      color: '#1572b6',\n      description: 'Responsive design, Flexbox, Grid, animations, and SCSS'\n    }, {\n      name: 'JavaScript',\n      level: 85,\n      icon: 'fab fa-js-square',\n      color: '#f7df1e',\n      description: 'ES6+, DOM manipulation, async programming, and modern JS'\n    }, {\n      name: 'Angular',\n      level: 88,\n      icon: 'fab fa-angular',\n      color: '#dd0031',\n      description: 'Components, services, routing, RxJS, and TypeScript'\n    }];\n    this.tools = [{\n      name: 'Git & GitHub',\n      icon: 'fab fa-git-alt'\n    }, {\n      name: 'VS Code',\n      icon: 'fas fa-code'\n    }, {\n      name: 'npm/yarn',\n      icon: 'fab fa-npm'\n    }, {\n      name: 'Chrome DevTools',\n      icon: 'fab fa-chrome'\n    }, {\n      name: 'Figma',\n      icon: 'fab fa-figma'\n    }, {\n      name: 'Responsive Design',\n      icon: 'fas fa-mobile-alt'\n    }];\n  }\n  ngOnInit() {\n    // Animate progress bars on component load\n    setTimeout(() => {\n      this.animateProgressBars();\n    }, 500);\n  }\n  animateProgressBars() {\n    const progressBars = document.querySelectorAll('.progress-fill');\n    progressBars.forEach((bar, index) => {\n      setTimeout(() => {\n        const skill = this.skills[index];\n        bar.style.width = skill.level + '%';\n      }, index * 200);\n    });\n  }\n  getCircularProgress(level) {\n    const circumference = 2 * Math.PI * 45; // radius = 45\n    const offset = circumference - level / 100 * circumference;\n    return `${circumference} ${offset}`;\n  }\n  static {\n    this.ɵfac = function SkillsComponent_Factory(t) {\n      return new (t || SkillsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SkillsComponent,\n      selectors: [[\"app-skills\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 54,\n      vars: 2,\n      consts: [[1, \"skills-hero\", \"section\"], [1, \"container\"], [1, \"section-title\"], [1, \"technical-skills\", \"section\"], [1, \"skills-grid\"], [\"class\", \"skill-card fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"tools-section\", \"section\"], [1, \"tools-grid\"], [\"class\", \"tool-card fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"learning-section\", \"section\"], [1, \"learning-content\"], [1, \"learning-card\", \"fade-in-up\"], [1, \"learning-icon\"], [1, \"fab\", \"fa-react\"], [1, \"learning-card\", \"fade-in-up\", 2, \"animation-delay\", \"0.2s\"], [1, \"fab\", \"fa-node-js\"], [1, \"learning-card\", \"fade-in-up\", 2, \"animation-delay\", \"0.4s\"], [1, \"fas\", \"fa-database\"], [1, \"skill-card\", \"fade-in-up\"], [1, \"skill-header\"], [1, \"skill-icon\"], [1, \"skill-info\"], [1, \"skill-percentage\"], [1, \"skill-description\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"circular-progress\"], [\"width\", \"100\", \"height\", \"100\"], [\"cx\", \"50\", \"cy\", \"50\", \"r\", \"45\", \"stroke\", \"#e0e0e0\", \"stroke-width\", \"8\", \"fill\", \"none\"], [\"cx\", \"50\", \"cy\", \"50\", \"r\", \"45\", \"stroke-width\", \"8\", \"fill\", \"none\", \"stroke-linecap\", \"round\", 1, \"progress-circle\"], [1, \"progress-text\"], [1, \"tool-card\", \"fade-in-up\"], [1, \"tool-icon\"]],\n      template: function SkillsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4, \"My Skills\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Technologies and tools I use to bring ideas to life\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(7, \"section\", 3)(8, \"div\", 1)(9, \"div\", 2)(10, \"h2\");\n          i0.ɵɵtext(11, \"Technical Skills\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\");\n          i0.ɵɵtext(13, \"Frontend technologies I've mastered and continue to improve\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 4);\n          i0.ɵɵtemplate(15, SkillsComponent_div_15_Template, 21, 15, \"div\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"section\", 6)(17, \"div\", 1)(18, \"div\", 2)(19, \"h2\");\n          i0.ɵɵtext(20, \"Tools & Technologies\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\");\n          i0.ɵɵtext(22, \"Development tools and technologies I work with daily\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 7);\n          i0.ɵɵtemplate(24, SkillsComponent_div_24_Template, 5, 5, \"div\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"section\", 9)(26, \"div\", 1)(27, \"div\", 2)(28, \"h2\");\n          i0.ɵɵtext(29, \"Currently Learning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"p\");\n          i0.ɵɵtext(31, \"Technologies I'm exploring to expand my skillset\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 10)(33, \"div\", 11)(34, \"div\", 12);\n          i0.ɵɵelement(35, \"i\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"h3\");\n          i0.ɵɵtext(37, \"React\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"p\");\n          i0.ɵɵtext(39, \"Exploring React ecosystem to broaden my frontend framework knowledge\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 14)(41, \"div\", 12);\n          i0.ɵɵelement(42, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"h3\");\n          i0.ɵɵtext(44, \"Node.js\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"p\");\n          i0.ɵɵtext(46, \"Learning backend development to become a full-stack developer\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 16)(48, \"div\", 12);\n          i0.ɵɵelement(49, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"h3\");\n          i0.ɵɵtext(51, \"Databases\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"p\");\n          i0.ɵɵtext(53, \"Understanding MongoDB and PostgreSQL for data management\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngForOf\", ctx.skills);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tools);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf],\n      styles: [\".skills-hero[_ngcontent-%COMP%] {\\n  padding-top: 120px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n\\n.technical-skills[_ngcontent-%COMP%] {\\n  background: white;\\n}\\n\\n.skills-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 40px;\\n}\\n\\n.skill-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 30px;\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.skill-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n.skill-card[_ngcontent-%COMP%]:hover   .circular-progress[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: scale(1);\\n}\\n.skill-card[_ngcontent-%COMP%]:hover   .progress-container[_ngcontent-%COMP%] {\\n  opacity: 0;\\n}\\n.skill-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.skill-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.skill-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  margin-right: 20px;\\n  width: 60px;\\n  text-align: center;\\n}\\n\\n.skill-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.skill-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  color: #2c3e50;\\n  margin-bottom: 5px;\\n}\\n.skill-info[_ngcontent-%COMP%]   .skill-percentage[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #667eea;\\n}\\n\\n.skill-description[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n}\\n.skill-description[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.6;\\n}\\n\\n.progress-container[_ngcontent-%COMP%] {\\n  transition: opacity 0.3s ease;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n  background: #e0e0e0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  width: 0%;\\n  border-radius: 4px;\\n  transition: width 1s ease-in-out;\\n}\\n\\n.circular-progress[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  right: 30px;\\n  transform: translateY(-50%) scale(0.8);\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.circular-progress[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n.circular-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%] {\\n  transition: stroke-dasharray 1s ease-in-out;\\n}\\n.circular-progress[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  font-weight: 600;\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n\\n.tools-section[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n}\\n\\n.tools-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 30px;\\n}\\n\\n.tool-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 30px 20px;\\n  border-radius: 15px;\\n  text-align: center;\\n  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n}\\n.tool-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);\\n}\\n.tool-card[_ngcontent-%COMP%]:hover   .tool-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n  color: #667eea;\\n}\\n.tool-card[_ngcontent-%COMP%]   .tool-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  color: #666;\\n  margin-bottom: 15px;\\n  transition: all 0.3s ease;\\n}\\n.tool-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n}\\n\\n.learning-section[_ngcontent-%COMP%] {\\n  background: white;\\n}\\n\\n.learning-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 40px;\\n}\\n\\n.learning-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 40px 30px;\\n  border-radius: 15px;\\n  text-align: center;\\n  transition: transform 0.3s ease;\\n}\\n.learning-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n.learning-card[_ngcontent-%COMP%]   .learning-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin-bottom: 20px;\\n  opacity: 0.9;\\n}\\n.learning-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin-bottom: 15px;\\n}\\n.learning-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  opacity: 0.9;\\n  line-height: 1.6;\\n}\\n\\n@media (max-width: 768px) {\\n  .skills-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .skill-card[_ngcontent-%COMP%] {\\n    padding: 25px 20px;\\n  }\\n  .skill-card[_ngcontent-%COMP%]   .circular-progress[_ngcontent-%COMP%] {\\n    position: static;\\n    transform: none;\\n    opacity: 1;\\n    margin-top: 20px;\\n    text-align: center;\\n  }\\n  .skill-card[_ngcontent-%COMP%]:hover   .progress-container[_ngcontent-%COMP%] {\\n    opacity: 1;\\n  }\\n  .tools-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 20px;\\n  }\\n  .learning-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .skill-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .skill-header[_ngcontent-%COMP%]   .skill-icon[_ngcontent-%COMP%] {\\n    margin-right: 0;\\n    margin-bottom: 15px;\\n  }\\n  .tools-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵstyleProp", "i_r2", "ɵɵadvance", "skill_r1", "color", "ɵɵclassMap", "icon", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "level", "description", "ctx_r2", "getCircularProgress", "i_r5", "tool_r4", "SkillsComponent", "constructor", "skills", "tools", "ngOnInit", "setTimeout", "animateProgressBars", "progressBars", "document", "querySelectorAll", "for<PERSON>ach", "bar", "index", "skill", "style", "width", "circumference", "Math", "PI", "offset", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SkillsComponent_Template", "rf", "ctx", "ɵɵtemplate", "SkillsComponent_div_15_Template", "SkillsComponent_div_24_Template", "ɵɵproperty", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\pages\\skills\\skills.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\pages\\skills\\skills.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\ninterface Skill {\n  name: string;\n  level: number;\n  icon: string;\n  color: string;\n  description: string;\n}\n\n@Component({\n  selector: 'app-skills',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './skills.component.html',\n  styleUrls: ['./skills.component.scss']\n})\nexport class SkillsComponent implements OnInit {\n  skills: Skill[] = [\n    {\n      name: 'HTML5',\n      level: 95,\n      icon: 'fab fa-html5',\n      color: '#e34f26',\n      description: 'Semantic markup, accessibility, and modern HTML5 features'\n    },\n    {\n      name: 'CSS3',\n      level: 90,\n      icon: 'fab fa-css3-alt',\n      color: '#1572b6',\n      description: 'Responsive design, Flexbox, Grid, animations, and SCSS'\n    },\n    {\n      name: 'JavaScript',\n      level: 85,\n      icon: 'fab fa-js-square',\n      color: '#f7df1e',\n      description: 'ES6+, DOM manipulation, async programming, and modern JS'\n    },\n    {\n      name: 'Angular',\n      level: 88,\n      icon: 'fab fa-angular',\n      color: '#dd0031',\n      description: 'Components, services, routing, RxJS, and TypeScript'\n    }\n  ];\n\n  tools = [\n    { name: 'Git & GitHub', icon: 'fab fa-git-alt' },\n    { name: 'VS Code', icon: 'fas fa-code' },\n    { name: 'npm/yarn', icon: 'fab fa-npm' },\n    { name: 'Chrome DevTools', icon: 'fab fa-chrome' },\n    { name: 'Figma', icon: 'fab fa-figma' },\n    { name: 'Responsive Design', icon: 'fas fa-mobile-alt' }\n  ];\n\n  ngOnInit() {\n    // Animate progress bars on component load\n    setTimeout(() => {\n      this.animateProgressBars();\n    }, 500);\n  }\n\n  animateProgressBars() {\n    const progressBars = document.querySelectorAll('.progress-fill');\n    progressBars.forEach((bar, index) => {\n      setTimeout(() => {\n        const skill = this.skills[index];\n        (bar as HTMLElement).style.width = skill.level + '%';\n      }, index * 200);\n    });\n  }\n\n  getCircularProgress(level: number): string {\n    const circumference = 2 * Math.PI * 45; // radius = 45\n    const offset = circumference - (level / 100) * circumference;\n    return `${circumference} ${offset}`;\n  }\n}\n", "<section class=\"skills-hero section\">\n  <div class=\"container\">\n    <div class=\"section-title\">\n      <h1>My Skills</h1>\n      <p>Technologies and tools I use to bring ideas to life</p>\n    </div>\n  </div>\n</section>\n\n<section class=\"technical-skills section\">\n  <div class=\"container\">\n    <div class=\"section-title\">\n      <h2>Technical Skills</h2>\n      <p>Frontend technologies I've mastered and continue to improve</p>\n    </div>\n    \n    <div class=\"skills-grid\">\n      <div class=\"skill-card fade-in-up\" *ngFor=\"let skill of skills; let i = index\" [style.animation-delay]=\"i * 0.1 + 's'\">\n        <div class=\"skill-header\">\n          <div class=\"skill-icon\" [style.color]=\"skill.color\">\n            <i [class]=\"skill.icon\"></i>\n          </div>\n          <div class=\"skill-info\">\n            <h3>{{ skill.name }}</h3>\n            <span class=\"skill-percentage\">{{ skill.level }}%</span>\n          </div>\n        </div>\n        \n        <div class=\"skill-description\">\n          <p>{{ skill.description }}</p>\n        </div>\n        \n        <div class=\"progress-container\">\n          <div class=\"progress-bar\">\n            <div class=\"progress-fill\" [style.background-color]=\"skill.color\"></div>\n          </div>\n        </div>\n        \n        <!-- Circular Progress -->\n        <div class=\"circular-progress\">\n          <svg width=\"100\" height=\"100\">\n            <circle cx=\"50\" cy=\"50\" r=\"45\" stroke=\"#e0e0e0\" stroke-width=\"8\" fill=\"none\"></circle>\n            <circle \n              cx=\"50\" \n              cy=\"50\" \n              r=\"45\" \n              [attr.stroke]=\"skill.color\"\n              stroke-width=\"8\" \n              fill=\"none\"\n              stroke-linecap=\"round\"\n              [style.stroke-dasharray]=\"getCircularProgress(skill.level)\"\n              class=\"progress-circle\">\n            </circle>\n          </svg>\n          <div class=\"progress-text\">{{ skill.level }}%</div>\n        </div>\n      </div>\n    </div>\n  </div>\n</section>\n\n<section class=\"tools-section section\">\n  <div class=\"container\">\n    <div class=\"section-title\">\n      <h2>Tools & Technologies</h2>\n      <p>Development tools and technologies I work with daily</p>\n    </div>\n    \n    <div class=\"tools-grid\">\n      <div class=\"tool-card fade-in-up\" *ngFor=\"let tool of tools; let i = index\" [style.animation-delay]=\"i * 0.1 + 's'\">\n        <div class=\"tool-icon\">\n          <i [class]=\"tool.icon\"></i>\n        </div>\n        <h4>{{ tool.name }}</h4>\n      </div>\n    </div>\n  </div>\n</section>\n\n<section class=\"learning-section section\">\n  <div class=\"container\">\n    <div class=\"section-title\">\n      <h2>Currently Learning</h2>\n      <p>Technologies I'm exploring to expand my skillset</p>\n    </div>\n    \n    <div class=\"learning-content\">\n      <div class=\"learning-card fade-in-up\">\n        <div class=\"learning-icon\">\n          <i class=\"fab fa-react\"></i>\n        </div>\n        <h3>React</h3>\n        <p>Exploring React ecosystem to broaden my frontend framework knowledge</p>\n      </div>\n      \n      <div class=\"learning-card fade-in-up\" style=\"animation-delay: 0.2s;\">\n        <div class=\"learning-icon\">\n          <i class=\"fab fa-node-js\"></i>\n        </div>\n        <h3>Node.js</h3>\n        <p>Learning backend development to become a full-stack developer</p>\n      </div>\n      \n      <div class=\"learning-card fade-in-up\" style=\"animation-delay: 0.4s;\">\n        <div class=\"learning-icon\">\n          <i class=\"fas fa-database\"></i>\n        </div>\n        <h3>Databases</h3>\n        <p>Understanding MongoDB and PostgreSQL for data management</p>\n      </div>\n    </div>\n  </div>\n</section>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;ICkBpCC,EAFJ,CAAAC,cAAA,cAAuH,cAC3F,cAC4B;IAClDD,EAAA,CAAAE,SAAA,QAA4B;IAC9BF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAwB,SAClB;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAErDJ,EAFqD,CAAAG,YAAA,EAAO,EACpD,EACF;IAGJH,EADF,CAAAC,cAAA,cAA+B,SAC1B;IAAAD,EAAA,CAAAI,MAAA,IAAuB;IAC5BJ,EAD4B,CAAAG,YAAA,EAAI,EAC1B;IAGJH,EADF,CAAAC,cAAA,eAAgC,eACJ;IACxBD,EAAA,CAAAE,SAAA,eAAwE;IAE5EF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,eAA+B;;IAC7BD,EAAA,CAAAC,cAAA,eAA8B;IAE5BD,EADA,CAAAE,SAAA,kBAAsF,kBAW7E;IACXF,EAAA,CAAAG,YAAA,EAAM;;IACNH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAI,MAAA,IAAkB;IAEjDJ,EAFiD,CAAAG,YAAA,EAAM,EAC/C,EACF;;;;;;IAvCyEH,EAAA,CAAAK,WAAA,oBAAAC,IAAA,aAAuC;IAE1FN,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAK,WAAA,UAAAG,QAAA,CAAAC,KAAA,CAA2B;IAC9CT,EAAA,CAAAO,SAAA,EAAoB;IAApBP,EAAA,CAAAU,UAAA,CAAAF,QAAA,CAAAG,IAAA,CAAoB;IAGnBX,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAY,iBAAA,CAAAJ,QAAA,CAAAK,IAAA,CAAgB;IACWb,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAc,kBAAA,KAAAN,QAAA,CAAAO,KAAA,MAAkB;IAKhDf,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAY,iBAAA,CAAAJ,QAAA,CAAAQ,WAAA,CAAuB;IAKGhB,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAK,WAAA,qBAAAG,QAAA,CAAAC,KAAA,CAAsC;IAgB/DT,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAK,WAAA,qBAAAY,MAAA,CAAAC,mBAAA,CAAAV,QAAA,CAAAO,KAAA,EAA2D;;IAIpCf,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAc,kBAAA,KAAAN,QAAA,CAAAO,KAAA,MAAkB;;;;;IAgB/Cf,EADF,CAAAC,cAAA,cAAoH,cAC3F;IACrBD,EAAA,CAAAE,SAAA,QAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,GAAe;IACrBJ,EADqB,CAAAG,YAAA,EAAK,EACpB;;;;;IALsEH,EAAA,CAAAK,WAAA,oBAAAc,IAAA,aAAuC;IAE5GnB,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAU,UAAA,CAAAU,OAAA,CAAAT,IAAA,CAAmB;IAEpBX,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAY,iBAAA,CAAAQ,OAAA,CAAAP,IAAA,CAAe;;;ADvD3B,OAAM,MAAOQ,eAAe;EAP5BC,YAAA;IAQE,KAAAC,MAAM,GAAY,CAChB;MACEV,IAAI,EAAE,OAAO;MACbE,KAAK,EAAE,EAAE;MACTJ,IAAI,EAAE,cAAc;MACpBF,KAAK,EAAE,SAAS;MAChBO,WAAW,EAAE;KACd,EACD;MACEH,IAAI,EAAE,MAAM;MACZE,KAAK,EAAE,EAAE;MACTJ,IAAI,EAAE,iBAAiB;MACvBF,KAAK,EAAE,SAAS;MAChBO,WAAW,EAAE;KACd,EACD;MACEH,IAAI,EAAE,YAAY;MAClBE,KAAK,EAAE,EAAE;MACTJ,IAAI,EAAE,kBAAkB;MACxBF,KAAK,EAAE,SAAS;MAChBO,WAAW,EAAE;KACd,EACD;MACEH,IAAI,EAAE,SAAS;MACfE,KAAK,EAAE,EAAE;MACTJ,IAAI,EAAE,gBAAgB;MACtBF,KAAK,EAAE,SAAS;MAChBO,WAAW,EAAE;KACd,CACF;IAED,KAAAQ,KAAK,GAAG,CACN;MAAEX,IAAI,EAAE,cAAc;MAAEF,IAAI,EAAE;IAAgB,CAAE,EAChD;MAAEE,IAAI,EAAE,SAAS;MAAEF,IAAI,EAAE;IAAa,CAAE,EACxC;MAAEE,IAAI,EAAE,UAAU;MAAEF,IAAI,EAAE;IAAY,CAAE,EACxC;MAAEE,IAAI,EAAE,iBAAiB;MAAEF,IAAI,EAAE;IAAe,CAAE,EAClD;MAAEE,IAAI,EAAE,OAAO;MAAEF,IAAI,EAAE;IAAc,CAAE,EACvC;MAAEE,IAAI,EAAE,mBAAmB;MAAEF,IAAI,EAAE;IAAmB,CAAE,CACzD;;EAEDc,QAAQA,CAAA;IACN;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,mBAAmB,EAAE;IAC5B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAA,mBAAmBA,CAAA;IACjB,MAAMC,YAAY,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,gBAAgB,CAAC;IAChEF,YAAY,CAACG,OAAO,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAI;MAClCP,UAAU,CAAC,MAAK;QACd,MAAMQ,KAAK,GAAG,IAAI,CAACX,MAAM,CAACU,KAAK,CAAC;QAC/BD,GAAmB,CAACG,KAAK,CAACC,KAAK,GAAGF,KAAK,CAACnB,KAAK,GAAG,GAAG;MACtD,CAAC,EAAEkB,KAAK,GAAG,GAAG,CAAC;IACjB,CAAC,CAAC;EACJ;EAEAf,mBAAmBA,CAACH,KAAa;IAC/B,MAAMsB,aAAa,GAAG,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAG,EAAE,CAAC,CAAC;IACxC,MAAMC,MAAM,GAAGH,aAAa,GAAItB,KAAK,GAAG,GAAG,GAAIsB,aAAa;IAC5D,OAAO,GAAGA,aAAa,IAAIG,MAAM,EAAE;EACrC;;;uBA9DWnB,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAoB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3C,EAAA,CAAA4C,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCftBlD,EAHN,CAAAC,cAAA,iBAAqC,aACZ,aACM,SACrB;UAAAD,EAAA,CAAAI,MAAA,gBAAS;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAI,MAAA,0DAAmD;UAG5DJ,EAH4D,CAAAG,YAAA,EAAI,EACtD,EACF,EACE;UAKJH,EAHN,CAAAC,cAAA,iBAA0C,aACjB,aACM,UACrB;UAAAD,EAAA,CAAAI,MAAA,wBAAgB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,mEAA2D;UAChEJ,EADgE,CAAAG,YAAA,EAAI,EAC9D;UAENH,EAAA,CAAAC,cAAA,cAAyB;UACvBD,EAAA,CAAAoD,UAAA,KAAAC,+BAAA,mBAAuH;UA0C7HrD,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKJH,EAHN,CAAAC,cAAA,kBAAuC,cACd,cACM,UACrB;UAAAD,EAAA,CAAAI,MAAA,4BAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,4DAAoD;UACzDJ,EADyD,CAAAG,YAAA,EAAI,EACvD;UAENH,EAAA,CAAAC,cAAA,cAAwB;UACtBD,EAAA,CAAAoD,UAAA,KAAAE,+BAAA,iBAAoH;UAQ1HtD,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKJH,EAHN,CAAAC,cAAA,kBAA0C,cACjB,cACM,UACrB;UAAAD,EAAA,CAAAI,MAAA,0BAAkB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,wDAAgD;UACrDJ,EADqD,CAAAG,YAAA,EAAI,EACnD;UAIFH,EAFJ,CAAAC,cAAA,eAA8B,eACU,eACT;UACzBD,EAAA,CAAAE,SAAA,aAA4B;UAC9BF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,4EAAoE;UACzEJ,EADyE,CAAAG,YAAA,EAAI,EACvE;UAGJH,EADF,CAAAC,cAAA,eAAqE,eACxC;UACzBD,EAAA,CAAAE,SAAA,aAA8B;UAChCF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,qEAA6D;UAClEJ,EADkE,CAAAG,YAAA,EAAI,EAChE;UAGJH,EADF,CAAAC,cAAA,eAAqE,eACxC;UACzBD,EAAA,CAAAE,SAAA,aAA+B;UACjCF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,gEAAwD;UAInEJ,EAJmE,CAAAG,YAAA,EAAI,EAC3D,EACF,EACF,EACE;;;UA/FiDH,EAAA,CAAAO,SAAA,IAAW;UAAXP,EAAA,CAAAuD,UAAA,YAAAJ,GAAA,CAAA5B,MAAA,CAAW;UAoDbvB,EAAA,CAAAO,SAAA,GAAU;UAAVP,EAAA,CAAAuD,UAAA,YAAAJ,GAAA,CAAA3B,KAAA,CAAU;;;qBDvDvDzB,YAAY,EAAAyD,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}