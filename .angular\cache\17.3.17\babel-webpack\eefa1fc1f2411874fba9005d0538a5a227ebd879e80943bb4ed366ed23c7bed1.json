{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nfunction ContactComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"i\", 20);\n    i0.ɵɵelementStart(2, \"h3\");\n    i0.ɵɵtext(3, \"Message Sent Successfully!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Thank you for reaching out. I'll get back to you soon.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ContactComponent_form_17_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"name\"), \" \");\n  }\n}\nfunction ContactComponent_form_17_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"email\"), \" \");\n  }\n}\nfunction ContactComponent_form_17_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"subject\"), \" \");\n  }\n}\nfunction ContactComponent_form_17_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"message\"), \" \");\n  }\n}\nfunction ContactComponent_form_17_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵtext(2, \" Send Message \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactComponent_form_17_span_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2, \" Sending... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactComponent_form_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 21);\n    i0.ɵɵlistener(\"ngSubmit\", function ContactComponent_form_17_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 22)(2, \"div\", 23)(3, \"label\", 24);\n    i0.ɵɵtext(4, \"Full Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 25);\n    i0.ɵɵtemplate(6, ContactComponent_form_17_div_6_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 23)(8, \"label\", 27);\n    i0.ɵɵtext(9, \"Email Address *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 28);\n    i0.ɵɵtemplate(11, ContactComponent_form_17_div_11_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 23)(13, \"label\", 29);\n    i0.ɵɵtext(14, \"Subject *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 30);\n    i0.ɵɵtemplate(16, ContactComponent_form_17_div_16_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 23)(18, \"label\", 31);\n    i0.ɵɵtext(19, \"Message *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"textarea\", 32);\n    i0.ɵɵtemplate(21, ContactComponent_form_17_div_21_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 33);\n    i0.ɵɵtemplate(23, ContactComponent_form_17_span_23_Template, 3, 0, \"span\", 34)(24, ContactComponent_form_17_span_24_Template, 3, 0, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.contactForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"error\", ctx_r1.f[\"name\"].invalid && ctx_r1.f[\"name\"].touched);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"name\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"error\", ctx_r1.f[\"email\"].invalid && ctx_r1.f[\"email\"].touched);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"email\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"error\", ctx_r1.f[\"subject\"].invalid && ctx_r1.f[\"subject\"].touched);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"subject\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"error\", ctx_r1.f[\"message\"].invalid && ctx_r1.f[\"message\"].touched);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getFieldError(\"message\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isSubmitting);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSubmitting);\n  }\n}\nfunction ContactComponent_div_25_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 42);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"href\", info_r3.link, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r3.value);\n  }\n}\nfunction ContactComponent_div_25_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const info_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(info_r3.value);\n  }\n}\nfunction ContactComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ContactComponent_div_25_a_6_Template, 2, 2, \"a\", 41)(7, ContactComponent_div_25_span_7_Template, 2, 1, \"span\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const info_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(info_r3.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(info_r3.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", info_r3.link !== \"#\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", info_r3.link === \"#\");\n  }\n}\nfunction ContactComponent_a_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 43);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const social_r4 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"background-color\", social_r4.color);\n    i0.ɵɵproperty(\"href\", social_r4.url, i0.ɵɵsanitizeUrl)(\"title\", social_r4.name);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(social_r4.icon);\n  }\n}\nexport let ContactComponent = /*#__PURE__*/(() => {\n  class ContactComponent {\n    constructor(fb) {\n      this.fb = fb;\n      this.isSubmitting = false;\n      this.isSubmitted = false;\n      this.contactInfo = [{\n        icon: 'fas fa-envelope',\n        title: 'Email',\n        value: '<EMAIL>',\n        link: 'mailto:<EMAIL>'\n      }, {\n        icon: 'fas fa-phone',\n        title: 'Phone',\n        value: '+****************',\n        link: 'tel:+15551234567'\n      }, {\n        icon: 'fas fa-map-marker-alt',\n        title: 'Location',\n        value: 'Your City, Country',\n        link: '#'\n      }];\n      this.socialLinks = [{\n        icon: 'fab fa-github',\n        name: 'GitHub',\n        url: 'https://github.com',\n        color: '#333'\n      }, {\n        icon: 'fab fa-linkedin',\n        name: 'LinkedIn',\n        url: 'https://linkedin.com',\n        color: '#0077b5'\n      }, {\n        icon: 'fab fa-twitter',\n        name: 'Twitter',\n        url: 'https://twitter.com',\n        color: '#1da1f2'\n      }, {\n        icon: 'fab fa-instagram',\n        name: 'Instagram',\n        url: 'https://instagram.com',\n        color: '#e4405f'\n      }];\n      this.contactForm = this.fb.group({\n        name: ['', [Validators.required, Validators.minLength(2)]],\n        email: ['', [Validators.required, Validators.email]],\n        subject: ['', [Validators.required, Validators.minLength(5)]],\n        message: ['', [Validators.required, Validators.minLength(10)]]\n      });\n    }\n    get f() {\n      return this.contactForm.controls;\n    }\n    onSubmit() {\n      if (this.contactForm.valid) {\n        this.isSubmitting = true;\n        // Simulate form submission\n        setTimeout(() => {\n          this.isSubmitting = false;\n          this.isSubmitted = true;\n          this.contactForm.reset();\n          // Reset success message after 5 seconds\n          setTimeout(() => {\n            this.isSubmitted = false;\n          }, 5000);\n        }, 2000);\n      } else {\n        // Mark all fields as touched to show validation errors\n        Object.keys(this.contactForm.controls).forEach(key => {\n          this.contactForm.get(key)?.markAsTouched();\n        });\n      }\n    }\n    getFieldError(fieldName) {\n      const field = this.contactForm.get(fieldName);\n      if (field?.errors && field.touched) {\n        if (field.errors['required']) {\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\n        }\n        if (field.errors['email']) {\n          return 'Please enter a valid email address';\n        }\n        if (field.errors['minlength']) {\n          const requiredLength = field.errors['minlength'].requiredLength;\n          return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${requiredLength} characters`;\n        }\n      }\n      return '';\n    }\n    openLink(url) {\n      if (url !== '#') {\n        window.open(url, '_blank');\n      }\n    }\n    static {\n      this.ɵfac = function ContactComponent_Factory(t) {\n        return new (t || ContactComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ContactComponent,\n        selectors: [[\"app-contact\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 38,\n        vars: 4,\n        consts: [[1, \"contact-hero\", \"section\"], [1, \"container\"], [1, \"section-title\"], [1, \"contact-content\", \"section\"], [1, \"contact-grid\"], [1, \"contact-form-section\", \"fade-in-up\"], [1, \"form-header\"], [\"class\", \"success-message\", 4, \"ngIf\"], [\"class\", \"contact-form\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"contact-info-section\", \"fade-in-up\", 2, \"animation-delay\", \"0.2s\"], [1, \"info-header\"], [1, \"contact-methods\"], [\"class\", \"contact-method\", 4, \"ngFor\", \"ngForOf\"], [1, \"social-section\"], [1, \"social-links\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", \"class\", \"social-link\", 3, \"href\", \"background-color\", \"title\", 4, \"ngFor\", \"ngForOf\"], [1, \"availability\"], [1, \"availability-status\"], [1, \"status-indicator\", \"available\"], [1, \"success-message\"], [1, \"fas\", \"fa-check-circle\"], [1, \"contact-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-row\"], [1, \"form-group\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", \"placeholder\", \"Your full name\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"for\", \"subject\"], [\"type\", \"text\", \"id\", \"subject\", \"formControlName\", \"subject\", \"placeholder\", \"What's this about?\"], [\"for\", \"message\"], [\"id\", \"message\", \"formControlName\", \"message\", \"rows\", \"6\", \"placeholder\", \"Tell me about your project or just say hello...\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"submit-btn\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"error-message\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"contact-method\"], [1, \"method-icon\"], [1, \"method-content\"], [3, \"href\", 4, \"ngIf\"], [3, \"href\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"social-link\", 3, \"href\", \"title\"]],\n        template: function ContactComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n            i0.ɵɵtext(4, \"Get In Touch\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"Let's discuss your next project or just say hello!\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(7, \"section\", 3)(8, \"div\", 1)(9, \"div\", 4)(10, \"div\", 5)(11, \"div\", 6)(12, \"h2\");\n            i0.ɵɵtext(13, \"Send Me a Message\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"p\");\n            i0.ɵɵtext(15, \"Fill out the form below and I'll get back to you as soon as possible.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(16, ContactComponent_div_16_Template, 6, 0, \"div\", 7)(17, ContactComponent_form_17_Template, 25, 16, \"form\", 8);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"h2\");\n            i0.ɵɵtext(21, \"Let's Connect\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"p\");\n            i0.ɵɵtext(23, \"Feel free to reach out through any of these channels.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 11);\n            i0.ɵɵtemplate(25, ContactComponent_div_25_Template, 8, 5, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 13)(27, \"h3\");\n            i0.ɵɵtext(28, \"Follow Me\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 14);\n            i0.ɵɵtemplate(30, ContactComponent_a_30_Template, 2, 6, \"a\", 15);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 16)(32, \"div\", 17);\n            i0.ɵɵelement(33, \"div\", 18);\n            i0.ɵɵelementStart(34, \"span\");\n            i0.ɵɵtext(35, \"Available for new projects\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(36, \"p\");\n            i0.ɵɵtext(37, \"I'm currently accepting new freelance projects and full-time opportunities.\");\n            i0.ɵɵelementEnd()()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngIf\", ctx.isSubmitted);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitted);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngForOf\", ctx.contactInfo);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.socialLinks);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n        styles: [\".contact-hero[_ngcontent-%COMP%]{padding-top:120px;background:linear-gradient(135deg,#f8f9fa,#e9ecef)}.contact-content[_ngcontent-%COMP%]{background:#fff}.contact-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:2fr 1fr;gap:60px;align-items:start}.contact-form-section[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]{margin-bottom:40px}.contact-form-section[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:2rem;color:#2c3e50;margin-bottom:10px}.contact-form-section[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;font-size:1.1rem}.success-message[_ngcontent-%COMP%]{text-align:center;padding:40px;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border-radius:15px;margin-bottom:30px}.success-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:20px}.success-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:10px}.success-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{opacity:.9}.contact-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:20px;margin-bottom:20px}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{margin-bottom:25px}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:8px;font-weight:500;color:#2c3e50}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:15px;border:2px solid #e9ecef;border-radius:10px;font-size:1rem;transition:all .3s ease;font-family:inherit}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:#667eea;box-shadow:0 0 0 3px #667eea1a}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input.error[_ngcontent-%COMP%], .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea.error[_ngcontent-%COMP%]{border-color:#e74c3c}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder{color:#adb5bd}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:vertical;min-height:120px}.contact-form[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#e74c3c;font-size:.875rem;margin-top:5px}.contact-form[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{width:100%;padding:15px;font-size:1.1rem;font-weight:600}.contact-form[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed}.contact-form[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:8px}.contact-info-section[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]{margin-bottom:40px}.contact-info-section[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.8rem;color:#2c3e50;margin-bottom:10px}.contact-info-section[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666}.contact-methods[_ngcontent-%COMP%]{margin-bottom:40px}.contact-method[_ngcontent-%COMP%]{display:flex;align-items:center;padding:20px;background:#f8f9fa;border-radius:10px;margin-bottom:15px;transition:all .3s ease}.contact-method[_ngcontent-%COMP%]:hover{background:#e9ecef;transform:translate(5px)}.contact-method[_ngcontent-%COMP%]   .method-icon[_ngcontent-%COMP%]{width:50px;height:50px;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:20px}.contact-method[_ngcontent-%COMP%]   .method-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff;font-size:1.2rem}.contact-method[_ngcontent-%COMP%]   .method-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#2c3e50;margin-bottom:5px;font-size:1.1rem}.contact-method[_ngcontent-%COMP%]   .method-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%], .contact-method[_ngcontent-%COMP%]   .method-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#667eea;text-decoration:none;font-weight:500}.contact-method[_ngcontent-%COMP%]   .method-content[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .contact-method[_ngcontent-%COMP%]   .method-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover{text-decoration:underline}.social-section[_ngcontent-%COMP%]{margin-bottom:40px}.social-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#2c3e50;margin-bottom:20px;font-size:1.3rem}.social-section[_ngcontent-%COMP%]   .social-links[_ngcontent-%COMP%]{display:flex;gap:15px}.social-section[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;text-decoration:none;transition:all .3s ease}.social-section[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]:hover{transform:translateY(-3px);box-shadow:0 10px 20px #0003}.social-section[_ngcontent-%COMP%]   .social-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem}.availability[_ngcontent-%COMP%]{padding:25px;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border-radius:15px}.availability[_ngcontent-%COMP%]   .availability-status[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:15px}.availability[_ngcontent-%COMP%]   .availability-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:50%;margin-right:10px}.availability[_ngcontent-%COMP%]   .availability-status[_ngcontent-%COMP%]   .status-indicator.available[_ngcontent-%COMP%]{background:#2ecc71;box-shadow:0 0 0 3px #2ecc714d}.availability[_ngcontent-%COMP%]   .availability-status[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:600}.availability[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{opacity:.9;margin:0;line-height:1.5}@media (max-width: 768px){.contact-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:40px}.contact-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:0}.social-links[_ngcontent-%COMP%]{justify-content:center}.contact-method[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.contact-method[_ngcontent-%COMP%]   .method-icon[_ngcontent-%COMP%]{margin-right:0;margin-bottom:15px}}@media (max-width: 480px){.contact-form-section[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%], .contact-form-section[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%], .contact-info-section[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%], .contact-info-section[_ngcontent-%COMP%]   .info-header[_ngcontent-%COMP%]{text-align:center}.contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .contact-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{padding:12px}}\"]\n      });\n    }\n  }\n  return ContactComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}