{"ast": null, "code": "import { RouterOutlet } from '@angular/router';\nimport { NavbarComponent } from './components/navbar/navbar.component';\nimport { FooterComponent } from './components/footer/footer.component';\nimport * as i0 from \"@angular/core\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'portfolio-website';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-navbar\");\n          i0.ɵɵelementStart(1, \"main\");\n          i0.ɵɵelement(2, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"app-footer\");\n        }\n      },\n      dependencies: [RouterOutlet, NavbarComponent, FooterComponent],\n      styles: [\"main[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 140px);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vLi4vLi4vTGFwdG9wJTIwSG91c2UvT25lRHJpdmUvRGVza3RvcC9teSUyMGZvbGlvL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsK0JBQUE7QUNDRiIsInNvdXJjZXNDb250ZW50IjpbIm1haW4ge1xuICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTQwcHgpOyAvLyBBZGp1c3QgYmFzZWQgb24gbmF2YmFyIGFuZCBmb290ZXIgaGVpZ2h0XG59XG4iLCJtYWluIHtcbiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDE0MHB4KTtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterOutlet", "NavbarComponent", "FooterComponent", "AppComponent", "constructor", "title", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { RouterOutlet } from '@angular/router';\nimport { NavbarComponent } from './components/navbar/navbar.component';\nimport { FooterComponent } from './components/footer/footer.component';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [RouterOutlet, NavbarComponent, FooterComponent],\n  template: `\n    <app-navbar></app-navbar>\n    <main>\n      <router-outlet></router-outlet>\n    </main>\n    <app-footer></app-footer>\n  `,\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent {\n  title = 'portfolio-website';\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,eAAe,QAAQ,sCAAsC;;AAetE,OAAM,MAAOC,YAAY;EAbzBC,YAAA;IAcE,KAAAC,KAAK,GAAG,mBAAmB;;;;uBADhBF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UARrBN,EAAA,CAAAQ,SAAA,iBAAyB;UACzBR,EAAA,CAAAS,cAAA,WAAM;UACJT,EAAA,CAAAQ,SAAA,oBAA+B;UACjCR,EAAA,CAAAU,YAAA,EAAO;UACPV,EAAA,CAAAQ,SAAA,iBAAyB;;;qBANjBjB,YAAY,EAAEC,eAAe,EAAEC,eAAe;MAAAkB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}