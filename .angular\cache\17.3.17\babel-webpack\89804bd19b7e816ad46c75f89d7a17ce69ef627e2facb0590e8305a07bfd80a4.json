{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { AppComponent } from './app/app.component';\nimport { provideRouter } from '@angular/router';\nimport { routes } from './app/app.routes';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes), provideAnimations()]\n}).catch(err => console.error(err));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}