{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let HomeComponent = /*#__PURE__*/(() => {\n  class HomeComponent {\n    constructor() {\n      this.typedText = '';\n      this.fullText = 'Frontend Web Developer | Angular Developer';\n      this.typingSpeed = 100;\n      this.deletingSpeed = 50;\n      this.pauseTime = 2000;\n      this.isDeleting = false;\n      this.charIndex = 0;\n    }\n    ngOnInit() {\n      this.startTyping();\n    }\n    ngOnDestroy() {\n      if (this.typingInterval) {\n        clearTimeout(this.typingInterval);\n      }\n    }\n    startTyping() {\n      const type = () => {\n        if (!this.isDeleting && this.charIndex < this.fullText.length) {\n          this.typedText += this.fullText.charAt(this.charIndex);\n          this.charIndex++;\n          this.typingInterval = setTimeout(type, this.typingSpeed);\n        } else if (this.isDeleting && this.charIndex > 0) {\n          this.typedText = this.fullText.substring(0, this.charIndex - 1);\n          this.charIndex--;\n          this.typingInterval = setTimeout(type, this.deletingSpeed);\n        } else {\n          this.isDeleting = !this.isDeleting;\n          this.typingInterval = setTimeout(type, this.pauseTime);\n        }\n      };\n      type();\n    }\n    scrollToAbout() {\n      // This will be handled by router navigation\n    }\n    static {\n      this.ɵfac = function HomeComponent_Factory(t) {\n        return new (t || HomeComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: HomeComponent,\n        selectors: [[\"app-home\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 24,\n        vars: 1,\n        consts: [[1, \"hero\"], [1, \"hero-background\"], [1, \"container\"], [1, \"hero-content\", \"fade-in-up\"], [1, \"hero-text\"], [1, \"hero-title\"], [1, \"highlight\"], [1, \"typing-container\"], [1, \"typed-text\"], [1, \"cursor\"], [1, \"hero-description\"], [1, \"hero-buttons\"], [\"routerLink\", \"/about\", 1, \"btn\", \"btn-primary\"], [\"routerLink\", \"/projects\", 1, \"btn\", \"btn-outline\"], [1, \"scroll-indicator\"], [1, \"scroll-arrow\"], [1, \"fas\", \"fa-chevron-down\"]],\n        template: function HomeComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"section\", 0);\n            i0.ɵɵelement(1, \"div\", 1);\n            i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h1\", 5);\n            i0.ɵɵtext(6, \" Hi, I'm \");\n            i0.ɵɵelementStart(7, \"span\", 6);\n            i0.ɵɵtext(8, \"Abdullah G\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 7)(10, \"span\", 8);\n            i0.ɵɵtext(11);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"span\", 9);\n            i0.ɵɵtext(13, \"|\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"p\", 10);\n            i0.ɵɵtext(15, \" Passionate about creating beautiful, responsive web applications with modern technologies. Welcome to my digital portfolio where creativity meets functionality. \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 11)(17, \"a\", 12);\n            i0.ɵɵtext(18, \"Learn More About Me\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"a\", 13);\n            i0.ɵɵtext(20, \"View My Work\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\", 15);\n            i0.ɵɵelement(23, \"i\", 16);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate(ctx.typedText);\n          }\n        },\n        dependencies: [CommonModule, RouterModule, i1.RouterLink],\n        styles: [\".hero[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;position:relative;overflow:hidden;padding-top:80px}.hero-background[_ngcontent-%COMP%]{position:absolute;inset:0;background:linear-gradient(135deg,#667eea,#764ba2);opacity:.1;z-index:-1}.hero-background[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"1\\\" fill=\\\"%23667eea\\\" opacity=\\\"0.1\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>')}.hero-content[_ngcontent-%COMP%]{width:100%;text-align:center;z-index:1}.hero-title[_ngcontent-%COMP%]{font-size:3.5rem;font-weight:700;color:#2c3e50;margin-bottom:20px;line-height:1.2}.hero-title[_ngcontent-%COMP%]   .highlight[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.typing-container[_ngcontent-%COMP%]{height:60px;margin-bottom:30px;display:flex;justify-content:center;align-items:center}.typed-text[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:500;color:#667eea}.cursor[_ngcontent-%COMP%]{font-size:1.5rem;color:#667eea;animation:_ngcontent-%COMP%_blink 1s infinite}@keyframes _ngcontent-%COMP%_blink{0%,50%{opacity:1}51%,to{opacity:0}}.hero-description[_ngcontent-%COMP%]{font-size:1.2rem;color:#666;max-width:600px;margin:0 auto 40px;line-height:1.6}.hero-buttons[_ngcontent-%COMP%]{display:flex;gap:20px;justify-content:center;flex-wrap:wrap}.scroll-indicator[_ngcontent-%COMP%]{position:absolute;bottom:30px;left:50%;transform:translate(-50%);animation:_ngcontent-%COMP%_bounce 2s infinite}.scroll-arrow[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:#667eea1a;display:flex;align-items:center;justify-content:center;color:#667eea;cursor:pointer;transition:all .3s ease}.scroll-arrow[_ngcontent-%COMP%]:hover{background:#667eea33;transform:translateY(-2px)}@keyframes _ngcontent-%COMP%_bounce{0%,20%,50%,80%,to{transform:translate(-50%) translateY(0)}40%{transform:translate(-50%) translateY(-10px)}60%{transform:translate(-50%) translateY(-5px)}}@media (max-width: 768px){.hero-title[_ngcontent-%COMP%]{font-size:2.5rem}.typed-text[_ngcontent-%COMP%], .cursor[_ngcontent-%COMP%]{font-size:1.2rem}.hero-description[_ngcontent-%COMP%]{font-size:1rem;padding:0 20px}.hero-buttons[_ngcontent-%COMP%]{flex-direction:column;align-items:center;gap:15px}.hero-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:200px}}@media (max-width: 480px){.hero-title[_ngcontent-%COMP%]{font-size:2rem}.typed-text[_ngcontent-%COMP%], .cursor[_ngcontent-%COMP%]{font-size:1rem}}\"]\n      });\n    }\n  }\n  return HomeComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}