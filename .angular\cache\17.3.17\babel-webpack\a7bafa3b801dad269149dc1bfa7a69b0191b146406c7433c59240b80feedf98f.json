{"ast": null, "code": "var EventEmitter = require(\"events\");\nmodule.exports = new EventEmitter();", "map": {"version": 3, "names": ["EventEmitter", "require", "module", "exports"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/my folio/node_modules/webpack/hot/emitter.js"], "sourcesContent": ["var EventEmitter = require(\"events\");\nmodule.exports = new EventEmitter();\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,QAAQ,CAAC;AACpCC,MAAM,CAACC,OAAO,GAAG,IAAIH,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}