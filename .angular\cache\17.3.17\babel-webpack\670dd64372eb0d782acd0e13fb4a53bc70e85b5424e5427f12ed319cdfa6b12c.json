{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction SkillsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 21)(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 22);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 23)(10, \"p\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 24)(13, \"div\", 25);\n    i0.ɵɵelement(14, \"div\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(16, \"svg\", 28);\n    i0.ɵɵelement(17, \"circle\", 29)(18, \"circle\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(19, \"div\", 31);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const skill_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"animation-delay\", i_r2 * 0.1 + \"s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", skill_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(skill_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(skill_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", skill_r1.level, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(skill_r1.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", skill_r1.color);\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"stroke-dasharray\", ctx_r2.getCircularProgress(skill_r1.level));\n    i0.ɵɵattribute(\"stroke\", skill_r1.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", skill_r1.level, \"%\");\n  }\n}\nfunction SkillsComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tool_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r5 * 0.1 + \"s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(tool_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tool_r4.name);\n  }\n}\nexport let SkillsComponent = /*#__PURE__*/(() => {\n  class SkillsComponent {\n    constructor() {\n      this.skills = [{\n        name: 'HTML5',\n        level: 95,\n        icon: 'fab fa-html5',\n        color: '#e34f26',\n        description: 'Semantic markup, accessibility, and modern HTML5 features'\n      }, {\n        name: 'CSS3',\n        level: 90,\n        icon: 'fab fa-css3-alt',\n        color: '#1572b6',\n        description: 'Responsive design, Flexbox, Grid, animations, and SCSS'\n      }, {\n        name: 'JavaScript',\n        level: 85,\n        icon: 'fab fa-js-square',\n        color: '#f7df1e',\n        description: 'ES6+, DOM manipulation, async programming, and modern JS'\n      }, {\n        name: 'Angular',\n        level: 88,\n        icon: 'fab fa-angular',\n        color: '#dd0031',\n        description: 'Components, services, routing, RxJS, and TypeScript'\n      }];\n      this.tools = [{\n        name: 'Git & GitHub',\n        icon: 'fab fa-git-alt'\n      }, {\n        name: 'VS Code',\n        icon: 'fas fa-code'\n      }, {\n        name: 'npm/yarn',\n        icon: 'fab fa-npm'\n      }, {\n        name: 'Chrome DevTools',\n        icon: 'fab fa-chrome'\n      }, {\n        name: 'Figma',\n        icon: 'fab fa-figma'\n      }, {\n        name: 'Responsive Design',\n        icon: 'fas fa-mobile-alt'\n      }];\n    }\n    ngOnInit() {\n      // Animate progress bars on component load\n      setTimeout(() => {\n        this.animateProgressBars();\n      }, 500);\n    }\n    animateProgressBars() {\n      const progressBars = document.querySelectorAll('.progress-fill');\n      progressBars.forEach((bar, index) => {\n        setTimeout(() => {\n          const skill = this.skills[index];\n          bar.style.width = skill.level + '%';\n        }, index * 200);\n      });\n    }\n    getCircularProgress(level) {\n      const circumference = 2 * Math.PI * 45; // radius = 45\n      const offset = circumference - level / 100 * circumference;\n      return `${circumference} ${offset}`;\n    }\n    static {\n      this.ɵfac = function SkillsComponent_Factory(t) {\n        return new (t || SkillsComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SkillsComponent,\n        selectors: [[\"app-skills\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 54,\n        vars: 2,\n        consts: [[1, \"skills-hero\", \"section\"], [1, \"container\"], [1, \"section-title\"], [1, \"technical-skills\", \"section\"], [1, \"skills-grid\"], [\"class\", \"skill-card fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"tools-section\", \"section\"], [1, \"tools-grid\"], [\"class\", \"tool-card fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"learning-section\", \"section\"], [1, \"learning-content\"], [1, \"learning-card\", \"fade-in-up\"], [1, \"learning-icon\"], [1, \"fab\", \"fa-react\"], [1, \"learning-card\", \"fade-in-up\", 2, \"animation-delay\", \"0.2s\"], [1, \"fab\", \"fa-node-js\"], [1, \"learning-card\", \"fade-in-up\", 2, \"animation-delay\", \"0.4s\"], [1, \"fas\", \"fa-database\"], [1, \"skill-card\", \"fade-in-up\"], [1, \"skill-header\"], [1, \"skill-icon\"], [1, \"skill-info\"], [1, \"skill-percentage\"], [1, \"skill-description\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"circular-progress\"], [\"width\", \"100\", \"height\", \"100\"], [\"cx\", \"50\", \"cy\", \"50\", \"r\", \"45\", \"stroke\", \"#e0e0e0\", \"stroke-width\", \"8\", \"fill\", \"none\"], [\"cx\", \"50\", \"cy\", \"50\", \"r\", \"45\", \"stroke-width\", \"8\", \"fill\", \"none\", \"stroke-linecap\", \"round\", 1, \"progress-circle\"], [1, \"progress-text\"], [1, \"tool-card\", \"fade-in-up\"], [1, \"tool-icon\"]],\n        template: function SkillsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n            i0.ɵɵtext(4, \"My Skills\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\");\n            i0.ɵɵtext(6, \"Technologies and tools I use to bring ideas to life\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(7, \"section\", 3)(8, \"div\", 1)(9, \"div\", 2)(10, \"h2\");\n            i0.ɵɵtext(11, \"Technical Skills\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"p\");\n            i0.ɵɵtext(13, \"Frontend technologies I've mastered and continue to improve\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(14, \"div\", 4);\n            i0.ɵɵtemplate(15, SkillsComponent_div_15_Template, 21, 15, \"div\", 5);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(16, \"section\", 6)(17, \"div\", 1)(18, \"div\", 2)(19, \"h2\");\n            i0.ɵɵtext(20, \"Tools & Technologies\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"p\");\n            i0.ɵɵtext(22, \"Development tools and technologies I work with daily\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(23, \"div\", 7);\n            i0.ɵɵtemplate(24, SkillsComponent_div_24_Template, 5, 5, \"div\", 8);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(25, \"section\", 9)(26, \"div\", 1)(27, \"div\", 2)(28, \"h2\");\n            i0.ɵɵtext(29, \"Currently Learning\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"p\");\n            i0.ɵɵtext(31, \"Technologies I'm exploring to expand my skillset\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(32, \"div\", 10)(33, \"div\", 11)(34, \"div\", 12);\n            i0.ɵɵelement(35, \"i\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"h3\");\n            i0.ɵɵtext(37, \"React\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"p\");\n            i0.ɵɵtext(39, \"Exploring React ecosystem to broaden my frontend framework knowledge\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 14)(41, \"div\", 12);\n            i0.ɵɵelement(42, \"i\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"h3\");\n            i0.ɵɵtext(44, \"Node.js\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"p\");\n            i0.ɵɵtext(46, \"Learning backend development to become a full-stack developer\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(47, \"div\", 16)(48, \"div\", 12);\n            i0.ɵɵelement(49, \"i\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"h3\");\n            i0.ɵɵtext(51, \"Databases\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(52, \"p\");\n            i0.ɵɵtext(53, \"Understanding MongoDB and PostgreSQL for data management\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngForOf\", ctx.skills);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngForOf\", ctx.tools);\n          }\n        },\n        dependencies: [CommonModule, i1.NgForOf],\n        styles: [\".skills-hero[_ngcontent-%COMP%]{padding-top:120px;background:linear-gradient(135deg,#f8f9fa,#e9ecef)}.technical-skills[_ngcontent-%COMP%]{background:#fff}.skills-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:40px}.skill-card[_ngcontent-%COMP%]{background:#fff;padding:30px;border-radius:15px;box-shadow:0 10px 30px #0000001a;transition:all .3s ease;position:relative;overflow:hidden}.skill-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 20px 40px #00000026}.skill-card[_ngcontent-%COMP%]:hover   .circular-progress[_ngcontent-%COMP%]{opacity:1;transform:scale(1)}.skill-card[_ngcontent-%COMP%]:hover   .progress-container[_ngcontent-%COMP%]{opacity:0}.skill-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:4px;background:linear-gradient(135deg,#667eea,#764ba2)}.skill-header[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:20px}.skill-icon[_ngcontent-%COMP%]{font-size:2.5rem;margin-right:20px;width:60px;text-align:center}.skill-info[_ngcontent-%COMP%]{flex:1}.skill-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.4rem;color:#2c3e50;margin-bottom:5px}.skill-info[_ngcontent-%COMP%]   .skill-percentage[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;color:#667eea}.skill-description[_ngcontent-%COMP%]{margin-bottom:25px}.skill-description[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;line-height:1.6}.progress-container[_ngcontent-%COMP%]{transition:opacity .3s ease}.progress-bar[_ngcontent-%COMP%]{width:100%;height:8px;background:#e0e0e0;border-radius:4px;overflow:hidden}.progress-fill[_ngcontent-%COMP%]{height:100%;width:0%;border-radius:4px;transition:width 1s ease-in-out}.circular-progress[_ngcontent-%COMP%]{position:absolute;top:50%;right:30px;transform:translateY(-50%) scale(.8);opacity:0;transition:all .3s ease}.circular-progress[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{transform:rotate(-90deg)}.circular-progress[_ngcontent-%COMP%]   .progress-circle[_ngcontent-%COMP%]{transition:stroke-dasharray 1s ease-in-out}.circular-progress[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);font-weight:600;color:#2c3e50;font-size:.9rem}.tools-section[_ngcontent-%COMP%]{background:#f8f9fa}.tools-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:30px}.tool-card[_ngcontent-%COMP%]{background:#fff;padding:30px 20px;border-radius:15px;text-align:center;box-shadow:0 5px 20px #00000014;transition:all .3s ease}.tool-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 15px 30px #00000026}.tool-card[_ngcontent-%COMP%]:hover   .tool-icon[_ngcontent-%COMP%]{transform:scale(1.1);color:#667eea}.tool-card[_ngcontent-%COMP%]   .tool-icon[_ngcontent-%COMP%]{font-size:2.5rem;color:#666;margin-bottom:15px;transition:all .3s ease}.tool-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#2c3e50;font-size:1.1rem;font-weight:500}.learning-section[_ngcontent-%COMP%]{background:#fff}.learning-content[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:40px}.learning-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:40px 30px;border-radius:15px;text-align:center;transition:transform .3s ease}.learning-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px)}.learning-card[_ngcontent-%COMP%]   .learning-icon[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:20px;opacity:.9}.learning-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:15px}.learning-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{opacity:.9;line-height:1.6}@media (max-width: 768px){.skills-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.skill-card[_ngcontent-%COMP%]{padding:25px 20px}.skill-card[_ngcontent-%COMP%]   .circular-progress[_ngcontent-%COMP%]{position:static;transform:none;opacity:1;margin-top:20px;text-align:center}.skill-card[_ngcontent-%COMP%]:hover   .progress-container[_ngcontent-%COMP%]{opacity:1}.tools-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);gap:20px}.learning-content[_ngcontent-%COMP%]{grid-template-columns:1fr}}@media (max-width: 480px){.skill-header[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.skill-header[_ngcontent-%COMP%]   .skill-icon[_ngcontent-%COMP%]{margin-right:0;margin-bottom:15px}.tools-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}\"]\n      });\n    }\n  }\n  return SkillsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}