{"ast": null, "code": "import { createOperatorSubscriber } from './OperatorSubscriber';\nexport function scanInternals(accumulator, seed, hasSeed, emitOnNext, emitBeforeComplete) {\n  return (source, subscriber) => {\n    let hasState = hasSeed;\n    let state = seed;\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const i = index++;\n      state = hasState ? accumulator(state, value, i) : (hasState = true, value);\n      emitOnNext && subscriber.next(state);\n    }, emitBeforeComplete && (() => {\n      hasState && subscriber.next(state);\n      subscriber.complete();\n    })));\n  };\n}", "map": {"version": 3, "names": ["createOperatorSubscriber", "scanInternals", "accumulator", "seed", "hasSeed", "emitOnNext", "emitBeforeComplete", "source", "subscriber", "hasState", "state", "index", "subscribe", "value", "i", "next", "complete"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/my folio/node_modules/rxjs/dist/esm/internal/operators/scanInternals.js"], "sourcesContent": ["import { createOperatorSubscriber } from './OperatorSubscriber';\nexport function scanInternals(accumulator, seed, hasSeed, emitOnNext, emitBeforeComplete) {\n    return (source, subscriber) => {\n        let hasState = hasSeed;\n        let state = seed;\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const i = index++;\n            state = hasState\n                ?\n                    accumulator(state, value, i)\n                :\n                    ((hasState = true), value);\n            emitOnNext && subscriber.next(state);\n        }, emitBeforeComplete &&\n            (() => {\n                hasState && subscriber.next(state);\n                subscriber.complete();\n            })));\n    };\n}\n"], "mappings": "AAAA,SAASA,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,aAAaA,CAACC,WAAW,EAAEC,IAAI,EAAEC,OAAO,EAAEC,UAAU,EAAEC,kBAAkB,EAAE;EACtF,OAAO,CAACC,MAAM,EAAEC,UAAU,KAAK;IAC3B,IAAIC,QAAQ,GAAGL,OAAO;IACtB,IAAIM,KAAK,GAAGP,IAAI;IAChB,IAAIQ,KAAK,GAAG,CAAC;IACbJ,MAAM,CAACK,SAAS,CAACZ,wBAAwB,CAACQ,UAAU,EAAGK,KAAK,IAAK;MAC7D,MAAMC,CAAC,GAAGH,KAAK,EAAE;MACjBD,KAAK,GAAGD,QAAQ,GAERP,WAAW,CAACQ,KAAK,EAAEG,KAAK,EAAEC,CAAC,CAAC,IAE1BL,QAAQ,GAAG,IAAI,EAAGI,KAAK,CAAC;MAClCR,UAAU,IAAIG,UAAU,CAACO,IAAI,CAACL,KAAK,CAAC;IACxC,CAAC,EAAEJ,kBAAkB,KAChB,MAAM;MACHG,QAAQ,IAAID,UAAU,CAACO,IAAI,CAACL,KAAK,CAAC;MAClCF,UAAU,CAACQ,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}