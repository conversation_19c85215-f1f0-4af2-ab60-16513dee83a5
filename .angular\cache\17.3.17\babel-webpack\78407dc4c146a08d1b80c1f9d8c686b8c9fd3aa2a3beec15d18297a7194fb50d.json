{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nfunction AboutComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 25);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r2 * 0.1 + \"s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.number);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.label);\n  }\n}\nfunction AboutComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"div\", 27);\n    i0.ɵɵelementStart(2, \"div\", 28)(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r4 * 0.2 + \"s\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r3.year);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.description);\n  }\n}\nexport class AboutComponent {\n  constructor() {\n    this.stats = [{\n      number: '2024',\n      label: 'Started Journey'\n    }, {\n      number: '10+',\n      label: 'Projects Completed'\n    }, {\n      number: '4+',\n      label: 'Technologies Mastered'\n    }, {\n      number: '100%',\n      label: 'Passion for Code'\n    }];\n    this.timeline = [{\n      year: '2024',\n      title: 'Started My Development Journey',\n      description: 'Began learning web development with a focus on modern frontend technologies and best practices.'\n    }, {\n      year: '2024',\n      title: 'Mastered Angular Framework',\n      description: 'Specialized in Angular development, creating responsive and dynamic web applications.'\n    }, {\n      year: '2024',\n      title: 'Launched Portfolio',\n      description: 'Created this portfolio to showcase my skills and projects to potential clients and employers.'\n    }];\n  }\n  static {\n    this.ɵfac = function AboutComponent_Factory(t) {\n      return new (t || AboutComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AboutComponent,\n      selectors: [[\"app-about\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 59,\n      vars: 2,\n      consts: [[1, \"about-hero\", \"section\"], [1, \"container\"], [1, \"about-content\"], [1, \"about-image\", \"fade-in\"], [1, \"image-placeholder\"], [1, \"fas\", \"fa-user\"], [1, \"about-text\", \"fade-in-up\"], [1, \"stats-section\"], [1, \"stats-grid\"], [\"class\", \"stat-item fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"journey-section\", \"section\"], [1, \"section-title\"], [1, \"timeline\"], [\"class\", \"timeline-item fade-in-up\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"education-section\", \"section\"], [1, \"education-content\"], [1, \"education-card\", \"fade-in-up\"], [1, \"card-icon\"], [1, \"fas\", \"fa-graduation-cap\"], [1, \"education-card\", \"fade-in-up\", 2, \"animation-delay\", \"0.2s\"], [1, \"fas\", \"fa-target\"], [1, \"education-card\", \"fade-in-up\", 2, \"animation-delay\", \"0.4s\"], [1, \"fas\", \"fa-rocket\"], [1, \"stat-item\", \"fade-in-up\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"timeline-item\", \"fade-in-up\"], [1, \"timeline-marker\"], [1, \"timeline-content\"], [1, \"timeline-year\"]],\n      template: function AboutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"h1\");\n          i0.ɵɵtext(8, \"About Me\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"h2\");\n          i0.ɵɵtext(10, \"Frontend Developer & Problem Solver\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\");\n          i0.ɵɵtext(12, \" Hello! I'm Abdullah G, a passionate frontend web developer who started my journey in 2024. I specialize in creating beautiful, responsive, and user-friendly web applications using modern technologies. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p\");\n          i0.ɵɵtext(14, \" My journey began with a curiosity about how websites work, and it quickly evolved into a passion for creating digital experiences that make a difference. I believe in writing clean, maintainable code and staying up-to-date with the latest industry trends and best practices. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p\");\n          i0.ɵɵtext(16, \" When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or planning my next big project. I'm always eager to learn and take on new challenges. \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(17, \"section\", 7)(18, \"div\", 1)(19, \"div\", 8);\n          i0.ɵɵtemplate(20, AboutComponent_div_20_Template, 5, 4, \"div\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"section\", 10)(22, \"div\", 1)(23, \"div\", 11)(24, \"h2\");\n          i0.ɵɵtext(25, \"My Journey\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"p\");\n          i0.ɵɵtext(27, \"From curiosity to expertise - here's how my development story unfolded\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 12);\n          i0.ɵɵtemplate(29, AboutComponent_div_29_Template, 9, 5, \"div\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"section\", 14)(31, \"div\", 1)(32, \"div\", 11)(33, \"h2\");\n          i0.ɵɵtext(34, \"Education & Goals\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"p\");\n          i0.ɵɵtext(36, \"Continuous learning and growth in the ever-evolving world of technology\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 15)(38, \"div\", 16)(39, \"div\", 17);\n          i0.ɵɵelement(40, \"i\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"h3\");\n          i0.ɵɵtext(42, \"Self-Taught Developer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \" Dedicated to continuous learning through online courses, documentation, and hands-on projects. Focused on mastering modern web development technologies and best practices. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 19)(46, \"div\", 17);\n          i0.ɵɵelement(47, \"i\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"h3\");\n          i0.ɵɵtext(49, \"Future Goals\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"p\");\n          i0.ɵɵtext(51, \" Aspiring to become a full-stack developer, contribute to meaningful open-source projects, and eventually start my own tech company to create innovative solutions. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 21)(53, \"div\", 17);\n          i0.ɵɵelement(54, \"i\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"h3\");\n          i0.ɵɵtext(56, \"Startup Vision\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"p\");\n          i0.ɵɵtext(58, \" Started my entrepreneurial journey in 2024 with the vision of creating web solutions that solve real-world problems and make technology accessible to everyone. \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(20);\n          i0.ɵɵproperty(\"ngForOf\", ctx.stats);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.timeline);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf],\n      styles: [\".about-hero[_ngcontent-%COMP%] {\\n  padding-top: 120px;\\n}\\n\\n.about-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 2fr;\\n  gap: 60px;\\n  align-items: center;\\n}\\n\\n.about-image[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.about-image[_ngcontent-%COMP%]   .image-placeholder[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: 300px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto;\\n  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);\\n}\\n.about-image[_ngcontent-%COMP%]   .image-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 120px;\\n  color: white;\\n}\\n\\n.about-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 700;\\n  color: #2c3e50;\\n  margin-bottom: 10px;\\n}\\n.about-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #667eea;\\n  margin-bottom: 30px;\\n  font-weight: 500;\\n}\\n.about-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  line-height: 1.8;\\n  color: #666;\\n  margin-bottom: 20px;\\n}\\n\\n.stats-section[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 80px 0;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 40px;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 30px 20px;\\n  background: white;\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.3s ease;\\n}\\n.stat-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n.stat-item[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  margin-bottom: 10px;\\n}\\n.stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n.journey-section[_ngcontent-%COMP%] {\\n  background: white;\\n}\\n\\n.timeline[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  position: relative;\\n}\\n.timeline[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 50%;\\n  top: 0;\\n  bottom: 0;\\n  width: 2px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  transform: translateX(-50%);\\n}\\n\\n.timeline-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 50px;\\n  position: relative;\\n}\\n.timeline-item[_ngcontent-%COMP%]:nth-child(odd) {\\n  flex-direction: row-reverse;\\n}\\n.timeline-item[_ngcontent-%COMP%]:nth-child(odd)   .timeline-content[_ngcontent-%COMP%] {\\n  text-align: right;\\n  margin-right: 40px;\\n}\\n.timeline-item[_ngcontent-%COMP%]:nth-child(even)   .timeline-content[_ngcontent-%COMP%] {\\n  margin-left: 40px;\\n}\\n\\n.timeline-marker[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  position: absolute;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  z-index: 2;\\n  box-shadow: 0 0 0 4px white, 0 0 0 8px #667eea;\\n}\\n\\n.timeline-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: white;\\n  padding: 30px;\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n.timeline-content[_ngcontent-%COMP%]   .timeline-year[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #667eea;\\n  font-weight: 600;\\n  margin-bottom: 10px;\\n}\\n.timeline-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  color: #2c3e50;\\n  margin-bottom: 15px;\\n}\\n.timeline-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.6;\\n}\\n\\n.education-section[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n}\\n\\n.education-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 40px;\\n}\\n\\n.education-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 40px 30px;\\n  border-radius: 15px;\\n  text-align: center;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  transition: transform 0.3s ease;\\n}\\n.education-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n.education-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto 25px;\\n}\\n.education-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  color: white;\\n}\\n.education-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  color: #2c3e50;\\n  margin-bottom: 20px;\\n}\\n.education-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  line-height: 1.6;\\n}\\n\\n@media (max-width: 768px) {\\n  .about-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 40px;\\n    text-align: center;\\n  }\\n  .about-image[_ngcontent-%COMP%]   .image-placeholder[_ngcontent-%COMP%] {\\n    width: 200px;\\n    height: 200px;\\n  }\\n  .about-image[_ngcontent-%COMP%]   .image-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 80px;\\n  }\\n  .about-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  .timeline[_ngcontent-%COMP%]::before {\\n    left: 20px;\\n  }\\n  .timeline-item[_ngcontent-%COMP%] {\\n    flex-direction: column !important;\\n    align-items: flex-start;\\n  }\\n  .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%] {\\n    margin-left: 50px !important;\\n    margin-right: 0 !important;\\n    text-align: left !important;\\n  }\\n  .timeline-marker[_ngcontent-%COMP%] {\\n    left: 20px !important;\\n    transform: translateX(-50%);\\n  }\\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 20px;\\n  }\\n  .education-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "i_r2", "ɵɵadvance", "ɵɵtextInterpolate", "stat_r1", "number", "label", "ɵɵelement", "i_r4", "item_r3", "year", "title", "description", "AboutComponent", "constructor", "stats", "timeline", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AboutComponent_Template", "rf", "ctx", "ɵɵtemplate", "AboutComponent_div_20_Template", "AboutComponent_div_29_Template", "ɵɵproperty", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\pages\\about\\about.component.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\my folio\\src\\app\\pages\\about\\about.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-about',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './about.component.html',\n  styleUrls: ['./about.component.scss']\n})\nexport class AboutComponent {\n  stats = [\n    { number: '2024', label: 'Started Journey' },\n    { number: '10+', label: 'Projects Completed' },\n    { number: '4+', label: 'Technologies Mastered' },\n    { number: '100%', label: 'Passion for Code' }\n  ];\n\n  timeline = [\n    {\n      year: '2024',\n      title: 'Started My Development Journey',\n      description: 'Began learning web development with a focus on modern frontend technologies and best practices.'\n    },\n    {\n      year: '2024',\n      title: 'Mastered Angular Framework',\n      description: 'Specialized in Angular development, creating responsive and dynamic web applications.'\n    },\n    {\n      year: '2024',\n      title: 'Launched Portfolio',\n      description: 'Created this portfolio to showcase my skills and projects to potential clients and employers.'\n    }\n  ];\n}\n", "<section class=\"about-hero section\">\n  <div class=\"container\">\n    <div class=\"about-content\">\n      <div class=\"about-image fade-in\">\n        <div class=\"image-placeholder\">\n          <i class=\"fas fa-user\"></i>\n        </div>\n      </div>\n      <div class=\"about-text fade-in-up\">\n        <h1>About Me</h1>\n        <h2>Frontend Developer & Problem Solver</h2>\n        <p>\n          Hello! I'm <PERSON>, a passionate frontend web developer who started my journey in 2024. \n          I specialize in creating beautiful, responsive, and user-friendly web applications using modern technologies.\n        </p>\n        <p>\n          My journey began with a curiosity about how websites work, and it quickly evolved into a passion for \n          creating digital experiences that make a difference. I believe in writing clean, maintainable code \n          and staying up-to-date with the latest industry trends and best practices.\n        </p>\n        <p>\n          When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, \n          or planning my next big project. I'm always eager to learn and take on new challenges.\n        </p>\n      </div>\n    </div>\n  </div>\n</section>\n\n<section class=\"stats-section\">\n  <div class=\"container\">\n    <div class=\"stats-grid\">\n      <div class=\"stat-item fade-in-up\" *ngFor=\"let stat of stats; let i = index\" [style.animation-delay]=\"i * 0.1 + 's'\">\n        <div class=\"stat-number\">{{ stat.number }}</div>\n        <div class=\"stat-label\">{{ stat.label }}</div>\n      </div>\n    </div>\n  </div>\n</section>\n\n<section class=\"journey-section section\">\n  <div class=\"container\">\n    <div class=\"section-title\">\n      <h2>My Journey</h2>\n      <p>From curiosity to expertise - here's how my development story unfolded</p>\n    </div>\n    \n    <div class=\"timeline\">\n      <div class=\"timeline-item fade-in-up\" *ngFor=\"let item of timeline; let i = index\" [style.animation-delay]=\"i * 0.2 + 's'\">\n        <div class=\"timeline-marker\"></div>\n        <div class=\"timeline-content\">\n          <div class=\"timeline-year\">{{ item.year }}</div>\n          <h3>{{ item.title }}</h3>\n          <p>{{ item.description }}</p>\n        </div>\n      </div>\n    </div>\n  </div>\n</section>\n\n<section class=\"education-section section\">\n  <div class=\"container\">\n    <div class=\"section-title\">\n      <h2>Education & Goals</h2>\n      <p>Continuous learning and growth in the ever-evolving world of technology</p>\n    </div>\n    \n    <div class=\"education-content\">\n      <div class=\"education-card fade-in-up\">\n        <div class=\"card-icon\">\n          <i class=\"fas fa-graduation-cap\"></i>\n        </div>\n        <h3>Self-Taught Developer</h3>\n        <p>\n          Dedicated to continuous learning through online courses, documentation, and hands-on projects. \n          Focused on mastering modern web development technologies and best practices.\n        </p>\n      </div>\n      \n      <div class=\"education-card fade-in-up\" style=\"animation-delay: 0.2s;\">\n        <div class=\"card-icon\">\n          <i class=\"fas fa-target\"></i>\n        </div>\n        <h3>Future Goals</h3>\n        <p>\n          Aspiring to become a full-stack developer, contribute to meaningful open-source projects, \n          and eventually start my own tech company to create innovative solutions.\n        </p>\n      </div>\n      \n      <div class=\"education-card fade-in-up\" style=\"animation-delay: 0.4s;\">\n        <div class=\"card-icon\">\n          <i class=\"fas fa-rocket\"></i>\n        </div>\n        <h3>Startup Vision</h3>\n        <p>\n          Started my entrepreneurial journey in 2024 with the vision of creating web solutions \n          that solve real-world problems and make technology accessible to everyone.\n        </p>\n      </div>\n    </div>\n  </div>\n</section>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;ICgCtCC,EADF,CAAAC,cAAA,cAAoH,cACzF;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChDH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAC1CF,EAD0C,CAAAG,YAAA,EAAM,EAC1C;;;;;IAHsEH,EAAA,CAAAI,WAAA,oBAAAC,IAAA,aAAuC;IACxFL,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAAO,iBAAA,CAAAC,OAAA,CAAAC,MAAA,CAAiB;IAClBT,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAC,OAAA,CAAAE,KAAA,CAAgB;;;;;IAc1CV,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAW,SAAA,cAAmC;IAEjCX,EADF,CAAAC,cAAA,cAA8B,cACD;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAE7BF,EAF6B,CAAAG,YAAA,EAAI,EACzB,EACF;;;;;IAP6EH,EAAA,CAAAI,WAAA,oBAAAQ,IAAA,aAAuC;IAG3FZ,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAM,OAAA,CAAAC,IAAA,CAAe;IACtCd,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAM,OAAA,CAAAE,KAAA,CAAgB;IACjBf,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAO,iBAAA,CAAAM,OAAA,CAAAG,WAAA,CAAsB;;;AD3CnC,OAAM,MAAOC,cAAc;EAP3BC,YAAA;IAQE,KAAAC,KAAK,GAAG,CACN;MAAEV,MAAM,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAiB,CAAE,EAC5C;MAAED,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAoB,CAAE,EAC9C;MAAED,MAAM,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAuB,CAAE,EAChD;MAAED,MAAM,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAkB,CAAE,CAC9C;IAED,KAAAU,QAAQ,GAAG,CACT;MACEN,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,gCAAgC;MACvCC,WAAW,EAAE;KACd,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,4BAA4B;MACnCC,WAAW,EAAE;KACd,EACD;MACEF,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE;KACd,CACF;;;;uBAxBUC,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAI,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAvB,EAAA,CAAAwB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNnB9B,EAJR,CAAAC,cAAA,iBAAoC,aACX,aACM,aACQ,aACA;UAC7BD,EAAA,CAAAW,SAAA,WAA2B;UAE/BX,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,aAAmC,SAC7B;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,2CAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,kNAEF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,4RAGF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,uMAEF;UAIRF,EAJQ,CAAAG,YAAA,EAAI,EACA,EACF,EACF,EACE;UAINH,EAFJ,CAAAC,cAAA,kBAA+B,cACN,cACG;UACtBD,EAAA,CAAAgC,UAAA,KAAAC,8BAAA,iBAAoH;UAM1HjC,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKJH,EAHN,CAAAC,cAAA,mBAAyC,cAChB,eACM,UACrB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,8EAAsE;UAC3EF,EAD2E,CAAAG,YAAA,EAAI,EACzE;UAENH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAgC,UAAA,KAAAE,8BAAA,kBAA2H;UAUjIlC,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKJH,EAHN,CAAAC,cAAA,mBAA2C,cAClB,eACM,UACrB;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,+EAAuE;UAC5EF,EAD4E,CAAAG,YAAA,EAAI,EAC1E;UAIFH,EAFJ,CAAAC,cAAA,eAA+B,eACU,eACd;UACrBD,EAAA,CAAAW,SAAA,aAAqC;UACvCX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,qLAEF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAGJH,EADF,CAAAC,cAAA,eAAsE,eAC7C;UACrBD,EAAA,CAAAW,SAAA,aAA6B;UAC/BX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,4KAEF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAGJH,EADF,CAAAC,cAAA,eAAsE,eAC7C;UACrBD,EAAA,CAAAW,SAAA,aAA6B;UAC/BX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvBH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,yKAEF;UAIRF,EAJQ,CAAAG,YAAA,EAAI,EACA,EACF,EACF,EACE;;;UAtE+CH,EAAA,CAAAM,SAAA,IAAU;UAAVN,EAAA,CAAAmC,UAAA,YAAAJ,GAAA,CAAAZ,KAAA,CAAU;UAgBNnB,EAAA,CAAAM,SAAA,GAAa;UAAbN,EAAA,CAAAmC,UAAA,YAAAJ,GAAA,CAAAX,QAAA,CAAa;;;qBD1C9DrB,YAAY,EAAAqC,EAAA,CAAAC,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}