{"ast": null, "code": "import { config } from '../config';\nimport { timeoutProvider } from '../scheduler/timeoutProvider';\nexport function reportUnhandledError(err) {\n  timeoutProvider.setTimeout(() => {\n    const {\n      onUnhandledError\n    } = config;\n    if (onUnhandledError) {\n      onUnhandledError(err);\n    } else {\n      throw err;\n    }\n  });\n}", "map": {"version": 3, "names": ["config", "timeout<PERSON>rovider", "reportUnhandledError", "err", "setTimeout", "onUnhandledError"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/my folio/node_modules/rxjs/dist/esm/internal/util/reportUnhandledError.js"], "sourcesContent": ["import { config } from '../config';\nimport { timeoutProvider } from '../scheduler/timeoutProvider';\nexport function reportUnhandledError(err) {\n    timeoutProvider.setTimeout(() => {\n        const { onUnhandledError } = config;\n        if (onUnhandledError) {\n            onUnhandledError(err);\n        }\n        else {\n            throw err;\n        }\n    });\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,WAAW;AAClC,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,OAAO,SAASC,oBAAoBA,CAACC,GAAG,EAAE;EACtCF,eAAe,CAACG,UAAU,CAAC,MAAM;IAC7B,MAAM;MAAEC;IAAiB,CAAC,GAAGL,MAAM;IACnC,IAAIK,gBAAgB,EAAE;MAClBA,gBAAgB,CAACF,GAAG,CAAC;IACzB,CAAC,MACI;MACD,MAAMA,GAAG;IACb;EACJ,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}